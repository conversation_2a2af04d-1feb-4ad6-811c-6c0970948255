/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : fw_file_handler.c
* @version 		  : 1.0.2
* @brief Handle the FW File Handling functionality
* @details Handle the FW File Handling functionality
*****************************************************************************
* @version 1.0.1                                Date : 24/07/2025
* Modified send_flash_task functionality
*****************************************************************************
* @version 1.0.2                                Date : 26/07/2025
* Sending Notification after File Transfer Complete
*****************************************************************************/
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "fw_file_handler.h"
#include "flash_handler.h"
#include "timer_handler.h"
#include "crc.h"
#include "utility.h"
#include "esp_log.h"
#include <string.h>

#define TAG "FLASH_WRITER"
#define FLASH_BLOCK_SIZE (1024*128)
#define ITEM_COUNT 40
#define ITEM_SIZE 256
extern uint8_t write_file_buffer[BUFFER_SIZE];
extern volatile bool ack_flag;
extern volatile bool nack_flag;
extern uint16_t req_resp_att_handle;


static uint32_t buffer_offset = 0;
static int active_half = 0; // 0 = first half, 1 = second half
static int counter = 0;

FILE *flash_fp = NULL;
extern QueueHandle_t file_event_queue;

extern uint16_t get_connection_handle(void);
extern void notify_client(uint16_t conn_handle, uint16_t attr_handle, uint8_t err_code);

/**
 * @brief FreeRTOS task that handles BLE data writing.
 *
 * This task is responsible for processing and writing incoming BLE data to
 * a target buffer or peripheral, such as flash memory, queues, or other storage.
 * It typically waits for notifications, messages, or data from BLE events and
 * performs the corresponding write operation.
 *
 * @param[in] param  Pointer to task-specific parameters (can be NULL if unused).
 *
 * @return void
 */
void ble_data_write_task(void *param)
{
    ESP_LOGI("FILE_HANDLE: ", "TASK CREATED");

    while (1) {
        if (ulTaskNotifyTakeIndexed(HALF_0_READY, pdTRUE, pdMS_TO_TICKS(10))) {
            ESP_LOGI("FILE_HANDLE: ", "Writing 1st Half: %d", (++counter) * FLASH_BLOCK_SIZE);
            current_packet_received(FLASH_BLOCK_SIZE);
            write_data_to_flash(&write_file_buffer[0], HALF_SIZE);
        }
        if (ulTaskNotifyTakeIndexed(HALF_1_READY, pdTRUE, pdMS_TO_TICKS(10))) {
            ESP_LOGI("FILE_HANDLE: ", "Writing 2nd Half: %d", (++counter) * FLASH_BLOCK_SIZE);
            write_data_to_flash(&write_file_buffer[HALF_SIZE], HALF_SIZE);
            current_packet_received(FLASH_BLOCK_SIZE);
        }
        if (ulTaskNotifyTakeIndexed(FINAL_FLUSH, pdTRUE, pdMS_TO_TICKS(10))) {
            ESP_LOGI("FILE_HANDLE: ", "Writing Last Packet: %d", (++counter) * FLASH_BLOCK_SIZE);
            // TO DETERMINE THE LAST WRITTEN HALF AND PARTIAL LENGTH
            int flushing_half = active_half;
            size_t base_offset = flushing_half * HALF_SIZE;
            ESP_LOGI("FILE_HANDLE: ", "Flushing final %ld bytes from half %d", buffer_offset, flushing_half);
            if (buffer_offset > 0) {
                write_data_to_flash(&write_file_buffer[base_offset], buffer_offset);
                current_packet_received(buffer_offset);
                buffer_offset = 0;  // reset
                notify_client(get_connection_handle(), req_resp_att_handle, BLE_FW_UPLOAD_SUCCESS);
            }
        }
    }
}

/**
 * @brief Marks the last received packet in a specific buffer half with an offset.
 *
 * This function is used to indicate that the last packet has been received,
 * typically in a double-buffered system. It updates internal tracking
 * using the specified buffer half and offset.
 *
 * @param[in] half    Identifier for the buffer half (e.g., 0 or 1).
 * @param[in] offset  Offset in bytes indicating the end position of the last packet.
 *
 * @return void
 */
void set_last_packet(uint8_t half, uint32_t offset) {
    active_half = half;
    buffer_offset = offset;
    ESP_LOGI("FW_FILE: ", "Last Packet: %ld", offset);
}

/**
 * @brief Sends flash data as part of a scheduled task.
 *
 * This function is responsible for reading data from flash memory and sending it
 * over a communication interface (e.g., BLE, UART, or network). It may be used
 * in a periodic task or triggered event to stream or transmit flash-stored content.
 *
 * @return int Returns 0 on successful transmission, or a negative error code on failure.
 */
int32_t start_fw_bundle_transfer(void) {
    BaseType_t xHigherPriorityTaskWoken = pdFALSE;
    uint32_t file_size = get_file_size();
    RTOSNotifyIndex evt = FW_CHUNK_START;
    if (file_size == 0){
        ESP_LOGE("ERROR: ", "FILE_CORRUPTION_ERROR");
        return FILE_CORRUPTION_ERROR;
    }
    ESP_LOGI("FW XFER: ", "START_FW_BUNDLE_TRANSFER");
    xQueueSendFromISR(file_event_queue, &evt, &xHigherPriorityTaskWoken);
    portYIELD_FROM_ISR(xHigherPriorityTaskWoken);
    return ESP_OK;
}

/**
 * @brief Erases a specified region or the entire flash memory.
 *
 * This function performs a flash erase operation. It may erase a specific sector,
 * block, or the entire flash chip depending on the implementation and target hardware.
 * Erasing flash is typically required before performing write operations.
 *
 * @return int32_t Returns 0 on success, or a negative error code on failure.
 */
int32_t erase_flash(void){
    counter = 0;
    return format_flash();
}
