/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : node_version.c
* @version 		  : 1.0.0
* @brief Handle the Node version functionality
* @details Handle the Node version functionality
*****************************************************************************/
#include "node_version.h"
#include "utility.h"
#include "esp_log.h"
#include "crc.h"
#include <string.h>

#define VERSION_INFO_SIZE (32)

static uint8_t version_info[HEADER_SIZE + VERSION_INFO_SIZE + CRC_LENGTH];

void store_version_info(uint8_t *data) {	
	version_info[START_BYTE_INDEX] = START_BYTE;
	version_info[PACKET_TYPE_INDEX] = BLE_VERSION_RESPONSE;
	u32_to_byte_array_little_endian(&version_info[LENGTH_START_INDEX], VERSION_INFO_SIZE);
	memcpy(&version_info[HEADER_SIZE], data, VERSION_INFO_SIZE);
	uint32_t calc_crc = crc32(version_info, HEADER_SIZE + VERSION_INFO_SIZE);
	u32_to_byte_array_little_endian(&version_info[HEADER_SIZE + VERSION_INFO_SIZE], calc_crc);
#ifdef DEBUG_CMD
	ESP_LOGI("BYTES RECEIVED: ", "%d", VERSION_INFO_SIZE);
#endif
}

uint8_t* get_version_info(uint32_t* length) {
    *length = sizeof(version_info);
	return &version_info[0];
}