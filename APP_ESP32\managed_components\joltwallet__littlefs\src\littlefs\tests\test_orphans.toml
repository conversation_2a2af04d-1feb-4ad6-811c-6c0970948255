[cases.test_orphans_normal]
in = "lfs.c"
if = 'PROG_SIZE <= 0x3fe' # only works with one crc per commit
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;
    lfs_mount(&lfs, cfg) => 0;
    lfs_mkdir(&lfs, "parent") => 0;
    lfs_mkdir(&lfs, "parent/orphan") => 0;
    lfs_mkdir(&lfs, "parent/child") => 0;
    lfs_remove(&lfs, "parent/orphan") => 0;
    lfs_unmount(&lfs) => 0;

    // corrupt the child's most recent commit, this should be the update
    // to the linked-list entry, which should orphan the orphan. Note this
    // makes a lot of assumptions about the remove operation.
    lfs_mount(&lfs, cfg) => 0;
    lfs_dir_t dir;
    lfs_dir_open(&lfs, &dir, "parent/child") => 0;
    lfs_block_t block = dir.m.pair[0];
    lfs_dir_close(&lfs, &dir) => 0;
    lfs_unmount(&lfs) => 0;
    uint8_t buffer[BLOCK_SIZE];
    cfg->read(cfg, block, 0, buffer, BLOCK_SIZE) => 0;
    int off = BLOCK_SIZE-1;
    while (off >= 0 && buffer[off] == ERASE_VALUE) {
        off -= 1;
    }
    memset(&buffer[off-3], BLOCK_SIZE, 3);
    cfg->erase(cfg, block) => 0;
    cfg->prog(cfg, block, 0, buffer, BLOCK_SIZE) => 0;
    cfg->sync(cfg) => 0;

    lfs_mount(&lfs, cfg) => 0;
    struct lfs_info info;
    lfs_stat(&lfs, "parent/orphan", &info) => LFS_ERR_NOENT;
    lfs_stat(&lfs, "parent/child", &info) => 0;
    lfs_fs_size(&lfs) => 8;
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_stat(&lfs, "parent/orphan", &info) => LFS_ERR_NOENT;
    lfs_stat(&lfs, "parent/child", &info) => 0;
    lfs_fs_size(&lfs) => 8;
    // this mkdir should both create a dir and deorphan, so size
    // should be unchanged
    lfs_mkdir(&lfs, "parent/otherchild") => 0;
    lfs_stat(&lfs, "parent/orphan", &info) => LFS_ERR_NOENT;
    lfs_stat(&lfs, "parent/child", &info) => 0;
    lfs_stat(&lfs, "parent/otherchild", &info) => 0;
    lfs_fs_size(&lfs) => 8;
    lfs_unmount(&lfs) => 0;

    lfs_mount(&lfs, cfg) => 0;
    lfs_stat(&lfs, "parent/orphan", &info) => LFS_ERR_NOENT;
    lfs_stat(&lfs, "parent/child", &info) => 0;
    lfs_stat(&lfs, "parent/otherchild", &info) => 0;
    lfs_fs_size(&lfs) => 8;
    lfs_unmount(&lfs) => 0;
'''

# test that we only run deorphan once per power-cycle
[cases.test_orphans_no_orphans]
in = 'lfs.c'
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;

    lfs_mount(&lfs, cfg) => 0;
    // mark the filesystem as having orphans
    lfs_fs_preporphans(&lfs, +1) => 0;
    lfs_mdir_t mdir;
    lfs_dir_fetch(&lfs, &mdir, (lfs_block_t[2]){0, 1}) => 0;
    lfs_dir_commit(&lfs, &mdir, NULL, 0) => 0;

    // we should have orphans at this state
    assert(lfs_gstate_hasorphans(&lfs.gstate));
    lfs_unmount(&lfs) => 0;

    // mount
    lfs_mount(&lfs, cfg) => 0;
    // we should detect orphans
    assert(lfs_gstate_hasorphans(&lfs.gstate));
    // force consistency
    lfs_fs_forceconsistency(&lfs) => 0;
    // we should no longer have orphans
    assert(!lfs_gstate_hasorphans(&lfs.gstate));

    lfs_unmount(&lfs) => 0;
'''

[cases.test_orphans_one_orphan]
in = 'lfs.c'
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;

    lfs_mount(&lfs, cfg) => 0;
    // create an orphan
    lfs_mdir_t orphan;
    lfs_alloc_ckpoint(&lfs);
    lfs_dir_alloc(&lfs, &orphan) => 0;
    lfs_dir_commit(&lfs, &orphan, NULL, 0) => 0;

    // append our orphan and mark the filesystem as having orphans
    lfs_fs_preporphans(&lfs, +1) => 0;
    lfs_mdir_t mdir;
    lfs_dir_fetch(&lfs, &mdir, (lfs_block_t[2]){0, 1}) => 0;
    lfs_pair_tole32(orphan.pair);
    lfs_dir_commit(&lfs, &mdir, LFS_MKATTRS(
            {LFS_MKTAG(LFS_TYPE_SOFTTAIL, 0x3ff, 8), orphan.pair})) => 0;

    // we should have orphans at this state
    assert(lfs_gstate_hasorphans(&lfs.gstate));
    lfs_unmount(&lfs) => 0;

    // mount
    lfs_mount(&lfs, cfg) => 0;
    // we should detect orphans
    assert(lfs_gstate_hasorphans(&lfs.gstate));
    // force consistency
    lfs_fs_forceconsistency(&lfs) => 0;
    // we should no longer have orphans
    assert(!lfs_gstate_hasorphans(&lfs.gstate));

    lfs_unmount(&lfs) => 0;
'''

# test that we can persist gstate with lfs_fs_mkconsistent
[cases.test_orphans_mkconsistent_no_orphans]
in = 'lfs.c'
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;

    lfs_mount(&lfs, cfg) => 0;
    // mark the filesystem as having orphans
    lfs_fs_preporphans(&lfs, +1) => 0;
    lfs_mdir_t mdir;
    lfs_dir_fetch(&lfs, &mdir, (lfs_block_t[2]){0, 1}) => 0;
    lfs_dir_commit(&lfs, &mdir, NULL, 0) => 0;

    // we should have orphans at this state
    assert(lfs_gstate_hasorphans(&lfs.gstate));
    lfs_unmount(&lfs) => 0;

    // mount
    lfs_mount(&lfs, cfg) => 0;
    // we should detect orphans
    assert(lfs_gstate_hasorphans(&lfs.gstate));
    // force consistency
    lfs_fs_mkconsistent(&lfs) => 0;
    // we should no longer have orphans
    assert(!lfs_gstate_hasorphans(&lfs.gstate));

    // remount
    lfs_unmount(&lfs) => 0;
    lfs_mount(&lfs, cfg) => 0;
    // we should still have no orphans
    assert(!lfs_gstate_hasorphans(&lfs.gstate));
    lfs_unmount(&lfs) => 0;
'''

[cases.test_orphans_mkconsistent_one_orphan]
in = 'lfs.c'
code = '''
    lfs_t lfs;
    lfs_format(&lfs, cfg) => 0;

    lfs_mount(&lfs, cfg) => 0;
    // create an orphan
    lfs_mdir_t orphan;
    lfs_alloc_ckpoint(&lfs);
    lfs_dir_alloc(&lfs, &orphan) => 0;
    lfs_dir_commit(&lfs, &orphan, NULL, 0) => 0;

    // append our orphan and mark the filesystem as having orphans
    lfs_fs_preporphans(&lfs, +1) => 0;
    lfs_mdir_t mdir;
    lfs_dir_fetch(&lfs, &mdir, (lfs_block_t[2]){0, 1}) => 0;
    lfs_pair_tole32(orphan.pair);
    lfs_dir_commit(&lfs, &mdir, LFS_MKATTRS(
            {LFS_MKTAG(LFS_TYPE_SOFTTAIL, 0x3ff, 8), orphan.pair})) => 0;

    // we should have orphans at this state
    assert(lfs_gstate_hasorphans(&lfs.gstate));
    lfs_unmount(&lfs) => 0;

    // mount
    lfs_mount(&lfs, cfg) => 0;
    // we should detect orphans
    assert(lfs_gstate_hasorphans(&lfs.gstate));
    // force consistency
    lfs_fs_mkconsistent(&lfs) => 0;
    // we should no longer have orphans
    assert(!lfs_gstate_hasorphans(&lfs.gstate));

    // remount
    lfs_unmount(&lfs) => 0;
    lfs_mount(&lfs, cfg) => 0;
    // we should still have no orphans
    assert(!lfs_gstate_hasorphans(&lfs.gstate));
    lfs_unmount(&lfs) => 0;
'''

# reentrant testing for orphans, basically just spam mkdir/remove
[cases.test_orphans_reentrant]
reentrant = true
# TODO fix this case, caused by non-DAG trees
if = '!(DEPTH == 3 && CACHE_SIZE != 64)'
defines = [
    {FILES=6,  DEPTH=1, CYCLES=20},
    {FILES=26, DEPTH=1, CYCLES=20},
    {FILES=3,  DEPTH=3, CYCLES=20},
]
code = '''
    lfs_t lfs;
    int err = lfs_mount(&lfs, cfg);
    if (err) {
        lfs_format(&lfs, cfg) => 0;
        lfs_mount(&lfs, cfg) => 0;
    }

    uint32_t prng = 1;
    const char alpha[] = "abcdefghijklmnopqrstuvwxyz";
    for (unsigned i = 0; i < CYCLES; i++) {
        // create random path
        char full_path[256];
        for (unsigned d = 0; d < DEPTH; d++) {
            sprintf(&full_path[2*d], "/%c", alpha[TEST_PRNG(&prng) % FILES]);
        }

        // if it does not exist, we create it, else we destroy
        struct lfs_info info;
        int res = lfs_stat(&lfs, full_path, &info);
        if (res == LFS_ERR_NOENT) {
            // create each directory in turn, ignore if dir already exists
            for (unsigned d = 0; d < DEPTH; d++) {
                char path[1024];
                strcpy(path, full_path);
                path[2*d+2] = '\0';
                err = lfs_mkdir(&lfs, path);
                assert(!err || err == LFS_ERR_EXIST);
            }

            for (unsigned d = 0; d < DEPTH; d++) {
                char path[1024];
                strcpy(path, full_path);
                path[2*d+2] = '\0';
                lfs_stat(&lfs, path, &info) => 0;
                assert(strcmp(info.name, &path[2*d+1]) == 0);
                assert(info.type == LFS_TYPE_DIR);
            }
        } else {
            // is valid dir?
            assert(strcmp(info.name, &full_path[2*(DEPTH-1)+1]) == 0);
            assert(info.type == LFS_TYPE_DIR);

            // try to delete path in reverse order, ignore if dir is not empty
            for (int d = DEPTH-1; d >= 0; d--) {
                char path[1024];
                strcpy(path, full_path);
                path[2*d+2] = '\0';
                err = lfs_remove(&lfs, path);
                assert(!err || err == LFS_ERR_NOTEMPTY);
            }

            lfs_stat(&lfs, full_path, &info) => LFS_ERR_NOENT;
        }
    }
    lfs_unmount(&lfs) => 0;
'''

