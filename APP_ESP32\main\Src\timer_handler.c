/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : timer_handler.c
* @version 		  : 1.0.1
* @brief Handle the Timer Handling functionality
* @details Handle the Timer Handling functionality
*****************************************************************************
* @version 1.0.1                                         				Date : 26/07/2025
* Removed unused variables
*****************************************************************************/

#include "timer_handler.h"

#define MIN_5_TIMER         (5 * 60 * 1000 * 1000)

esp_timer_handle_t timer_handle;
static uint32_t timer_counter = 0;

esp_err_t timer_initalization(void) {
    const esp_timer_create_args_t timer_args = {
        .callback = &timer_callback,
        .arg = NULL,
        .dispatch_method = ESP_TIMER_TASK,
        .name = "5_min_timer"
    };

    esp_err_t err = esp_timer_create(&timer_args, &timer_handle);
    if (err != ESP_OK) {
        ESP_LOGE("Timer", "Failed to create timer: %s", esp_err_to_name(err));
    }
    return err;
}



void timer_callback(void* arg) {
    ESP_LOGI("Timer", "Timer was stopped before elapsing");
}

void timer_start(uint32_t time_in_ms) {
    esp_timer_start_once(timer_handle, time_in_ms * 1000);
}

void timer_stop(void) {
    esp_timer_stop(timer_handle);
}
