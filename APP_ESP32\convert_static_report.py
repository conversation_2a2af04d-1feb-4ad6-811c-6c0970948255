import re
import csv

# ==== Configuration ====
input_txt = '20_KW_Vantage_BLE_Nexus_ESP32_Report.pdf'   # Replace with your extracted text file path
output_csv = 'StaticAnalysis_Report.csv'

# Optional: Adjust this prefix to trim file paths to relative repo paths
repo_prefix = 'D:\\BLE\\Bitbucket\\Feature_ble_fw_esp32\\APP_ESP32'

# Map checker prefixes to issue categories and descriptions:
checker_mapping = {
    'NPD': ("Null pointer dereference", "Potential null pointer dereference"),
    'MLK': ("Memory leak", "Possible memory leak"),
    'ABV': ("Array index out of bounds", "Array index violation"),
    'FUNCRET': ("Function return issue", "Implicit or missing function return"),
    'SV': ("Static analysis warning", "Static check warning"),
    'CL': ("Concurrency issue", "Potential concurrency error"),
    'INFINITE_LOOP': ("Infinite loop", "Potential infinite loop detected"),
    'RETVOID': ("Void return issue", "Implicit void return in non-void function"),
    'UNINIT': ("Uninitialized variable", "Variable used uninitialized"),
    'CWARN': ("Coding warning", "Coding style or semantics warning"),
    'RNPD': ("Risky null pointer dereference", "Suspicious null pointer usage"),
    'UFM': ("Use after free", "Use of freed memory"),
    'RH': ("Resource handling issue", "Resource leak or mishandling"),
    'VOIDRET': ("Void function returns value", "Void function returning value error"),
    # Add or modify as per your checker codes
}

def get_issue_info(checker_name):
    for prefix, (issue, desc) in checker_mapping.items():
        if checker_name.startswith(prefix):
            return issue, desc
    # If unknown checker, fallback
    return checker_name.split('.')[0], "Refer checker details"

def process_report(input_file, output_file):
    with open(input_file, 'r', encoding='latin-1') as infile, \
         open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
        
        writer = csv.writer(csvfile)
        # Write CSV header (matching your Excel template)
        writer.writerow([
            "Sr.No.","File Name","Description","Line No.","Issue ID","Checker Name","Action",
            "Checker Description","Priority","Folder","Issue","Scope","Softdel Comments"
        ])

        content = infile.read()
        # Split the content into blocks for each issue
        issues = content.split('---------------------------------------------------------------------------')

        sr_no = 1
        for issue in issues:
            # Regex pattern to extract fields from each issue block
            # Explanation:
            # 1) Issue number or Local
            # 2) Optional System ID number
            # 3) Full file path
            # 4) Line number
            # 5) Checker name/code
            # 6) Severity number (unused)
            # 7) Severity text (Critical, Error, Review, Warning)
            # 8) Action (Analyze)
            # 9) Description text (multi-line, till 'Current status')
            pattern = re.compile(
                r'(\d+|Local)\s+\(System: (\d+)?\)?\s+'           # Issue No and optional System ID
                r'([^\s:]+(?:/[^\s:]+)*):'                        # File path
                r'(\d+)\s+'                                       # Line number
                r'(\S+)\s+'                                      # Checker Name
                r'\((\d+):(\w+)\)\s+'                             # Severity (num and text)
                r'(\w+)\n'                                        # Action
                r'(.+?)\nCurrent status',                         # Description (till 'Current status')
                re.DOTALL
            )

            match = pattern.search(issue)
            if match:
                issue_no, system_id, full_path, line_no, checker_name, sev_num, severity, action, desc_text = match.groups()

                # Normalize file path to relative by removing prefix
                if full_path.startswith(repo_prefix):
                    file_rel = full_path[len(repo_prefix):]
                else:
                    file_rel = full_path

                # Extract folder (top-level directory in relative path)
                folder = file_rel.split('/')[0] if '/' in file_rel else ''

                # Get issue category and checker description from mapping
                issue_type, checker_desc = get_issue_info(checker_name)

                # Clean description: replace curly quotes and newlines
                cleaned_desc = desc_text.strip().replace('\n', ' ').replace('’', "'").replace('‘', "'")

                # Normalize severity text capitalization
                priority = severity.capitalize()

                # Write the CSV row
                writer.writerow([
                    sr_no,
                    file_rel,
                    cleaned_desc,
                    line_no,
                    system_id if system_id else '',
                    checker_name,
                    action,
                    checker_desc,
                    priority,
                    folder,
                    issue_type,
                    '',     # Scope
                    ''      # Softdel Comments
                ])

                sr_no += 1

        print(f"CSV file '{output_file}' generated successfully with {sr_no - 1} entries.")

if __name__ == '__main__':
    process_report(input_txt, output_csv)
