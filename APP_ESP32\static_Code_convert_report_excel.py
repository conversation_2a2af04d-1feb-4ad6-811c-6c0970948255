import pdfplumber
import pandas as pd
import re
from pathlib import Path
from datetime import datetime

# Excel template columns
columns = [
    "Sr. No.", "File Name", "Description", "Line No.", "Issue ID",
    "Checker Name", "Action", "Checker Description", "Priority",
    "Folder", "Issue", "Scope", "Comments"
]

def extract_pdf_text(pdf_path):
    """Extract text from PDF with improved layout handling"""
    all_text = []
    with pdfplumber.open(pdf_path) as pdf:
        for page in pdf.pages:
            text = page.extract_text(layout=True)  # Use layout mode
            if text:
                all_text.append(text)
            else:
                # Try alternative extraction if normal fails
                text = page.extract_text(x_tolerance=2, y_tolerance=2)
                if text:
                    all_text.append(text)
    return "\n".join(all_text)

def group_issue_blocks(lines):
    """Improved block grouping with header detection"""
    blocks = []
    current_block = []
    separator_pattern = re.compile(r'^-{3,}$')  # 3+ dashes
    
    for line in lines:
        if separator_pattern.match(line.strip()):
            if current_block:
                blocks.append(current_block)
                current_block = []
        else:
            if line.strip():
                current_block.append(line.strip())
    
    # Add final block if exists
    if current_block:
        blocks.append(current_block)
    
    return blocks

def parse_issue_block(block, sr_no):
    """More robust issue parsing with multiple pattern support"""
    if not block:
        return None

    # Combine all patterns into one with named groups
    master_pattern = re.compile(
        r'(?:^(?P<file>.+?):(?P<line>\d+)\s+'  # File:line
        r'(?P<checker>[A-Z0-9_.-]+)\s*'  # Checker name
        r'\((?P<id>\d+):(?P<priority>Error|Critical|Info|Warning|Review|High|Medium|Low)\)'  # ID:Priority
        r'|^(?P<alt_file>.+?):(?P<alt_line>\d+)\s+'  # Alternative pattern
        r'(?P<alt_checker>[A-Z0-9_.-]+)\s*'
        r'\((?P<alt_id>\d+)\)\s*'
        r'(?P<alt_priority>Error|Critical|Info|Warning|Review|High|Medium|Low))',
        re.IGNORECASE | re.MULTILINE
    )

    # Search through all lines in the block
    for line in block:
        match = master_pattern.search(line)
        if match:
            # Use main pattern groups or alternative pattern groups
            file_path = match.group('file') or match.group('alt_file')
            line_no = match.group('line') or match.group('alt_line')
            checker_name = match.group('checker') or match.group('alt_checker')
            issue_id = match.group('id') or match.group('alt_id')
            priority = (match.group('priority') or match.group('alt_priority')).capitalize()

            # Get description (all lines before match)
            header_idx = block.index(line)
            description = ' '.join(block[:header_idx]).strip()
            description = re.sub(r'\s+', ' ', description)

            # Format file path
            folder = Path(file_path).parts[0] if file_path else ""
            file_path_quoted = f'"""{file_path}"""'

            return [
                sr_no, file_path_quoted, description, line_no, issue_id,
                checker_name, "Analyze", "Static Analysis Finding", priority,
                folder, "", "", ""
            ]
    
    # If no match found, try to handle metadata blocks
    if "Report.txt" in block[0] and len(block) > 1:
        # This handles your first unmatched block case
        for line in block[1:]:
            match = master_pattern.search(line)
            if match:
                return parse_issue_block(block[1:], sr_no)
    
    return None

def process_unmatched_blocks(unmatched_blocks, start_sr):
    """Special handling for blocks that didn't match initially"""
    data = []
    sr_no = start_sr
    
    for block in unmatched_blocks:
        # Try again with more flexible parsing
        row = parse_issue_block(block, sr_no)
        if row:
            data.append(row)
            sr_no += 1
        else:
            # Save completely unmatched blocks for manual review
            with open("manual_review_blocks.txt", "a", encoding="utf-8") as f:
                f.write("\n".join(block) + "\n" + "="*80 + "\n")
    
    return data

def main(pdf_path):
    print("\n" + "="*50)
    print("Starting Enhanced PDF Processing")
    print("="*50)
    
    # Generate timestamped output filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_excel = f"static_analysis_report_{timestamp}.xlsx"
    
    # Step 1: Extract and clean text
    pdf_text = extract_pdf_text(pdf_path)
    lines = [line.strip() for line in pdf_text.splitlines() if line.strip()]
    print(f"\nExtracted {len(lines)} non-empty lines from PDF")
    
    # Step 2: Group into blocks
    blocks = group_issue_blocks(lines)
    print(f"Grouped into {len(blocks)} potential issue blocks")
    
    # Step 3: Parse all blocks
    parsed_data = []
    unmatched_blocks = []
    sr_no = 1
    
    print("\nParsing issue blocks...")
    for i, block in enumerate(blocks):
        row = parse_issue_block(block, sr_no)
        if row:
            parsed_data.append(row)
            sr_no += 1
        else:
            unmatched_blocks.append(block)
            if i < 5:  # Show first 5 unmatched for debugging
                print(f"Initial unmatched block {i+1}:")
                print("\n".join(block[:3]) + "...")
    
    print(f"\nInitially parsed {len(parsed_data)} issues")
    print(f"Found {len(unmatched_blocks)} unmatched blocks")
    
    # Step 4: Special processing for unmatched blocks
    if unmatched_blocks:
        print("\nProcessing unmatched blocks with special handling...")
        additional_data = process_unmatched_blocks(unmatched_blocks, sr_no)
        parsed_data.extend(additional_data)
        print(f"Parsed {len(additional_data)} additional issues from unmatched blocks")
    
    # Final output
    total_issues = len(parsed_data)
    print(f"\nTotal issues processed: {total_issues}")
    
    # Create DataFrame and save
    df = pd.DataFrame(parsed_data, columns=columns)
    df.to_excel(output_excel, index=False)
    print(f"\nSaved results to '{output_excel}'")
    
    # Summary
    print("\n" + "="*50)
    print("Final Processing Summary")
    print("="*50)
    print(f"Total blocks identified: {len(blocks)}")
    print(f"Successfully parsed: {total_issues}")
    print(f"Unmatched blocks: {len(unmatched_blocks) - len(additional_data)}")
    print("\nProcessing complete!")

if __name__ == "__main__":
    pdf_file = "20_KW_Vantage_BLE_Nexus_ESP32_Report.pdf"
    main(pdf_file)