# ESP32 BLE OTA User Guide

**Version:** 2.0  
**Date:** July 31, 2025  
**Target Audience:** End Users, Developers, System Integrators  

## 📋 Table of Contents

1. [Quick Start](#quick-start)
2. [Prerequisites](#prerequisites)
3. [Setup Instructions](#setup-instructions)
4. [Performing OTA Updates](#performing-ota-updates)
5. [Advanced Usage](#advanced-usage)
6. [Troubleshooting](#troubleshooting)
7. [Best Practices](#best-practices)

## 🚀 Quick Start

### For Immediate OTA Update:

1. **Ensure ESP32 is powered and advertising BLE**
2. **Navigate to the project directory:**
   ```bash
   cd APP_ESP32
   ```
3. **Run the batch file for easy firmware selection:**
   ```bash
   Ota_flash_firmware_select.bat
   ```
4. **Follow the on-screen prompts to select firmware and target device**

**Expected Result:** 3.6MB firmware transfers in ~90 seconds at 46KB/s

## 📋 Prerequisites

### Hardware Requirements
- **ESP32 Device:** ESP32, ESP32-S3, or ESP32-C3 with BLE enabled
- **Computer:** Windows, Linux, or macOS with Bluetooth 4.0+
- **Power Supply:** Stable power source for ESP32 during update

### Software Requirements
- **Python:** Version 3.8 or higher
- **ESP-IDF:** Version 5.4.1+ (for ESP32 development)
- **BLE Adapter:** Built-in or USB Bluetooth adapter

### Python Dependencies
```bash
pip install bleak asyncio
```

## 🔧 Setup Instructions

### 1. ESP32 Setup

#### Build and Flash Initial Firmware
```bash
cd APP_ESP32
idf.py build
idf.py flash
```

#### Verify BLE OTA Service
```bash
idf.py monitor
```

**Expected Output:**
```
I (xxx) ota: OTA manager initialized successfully
I (xxx) ble_ota: BLE OTA service initialized successfully
I (xxx) ble_app: BLE advertising started
```

### 2. Python Client Setup

#### Install Dependencies
```bash
# Navigate to project directory
cd APP_ESP32

# Install required packages
pip install bleak asyncio
```

#### Verify Installation
```bash
python nimble_ota_client.py --scan
```

**Expected Output:**
```
🔍 Scanning for ESP32 devices...
Found target devices:
  MR Car 7 - A0:85:E3:F1:8C:C6
```

## 📱 Performing OTA Updates

### Method 1: Batch File (Recommended)

#### Step 1: Launch Batch File
```bash
Ota_flash_firmware_select.bat
```

#### Step 2: Select Firmware
The script will display available firmware files:
```
Available firmware files:
1. fw_0_0_28_0_e42d49c7.bin (3.6 MB)
2. nexus_fw_0_0_45_0_558bdb81.bin (2.1 MB)
Enter your choice (1-2):
```

#### Step 3: Select Target Device
```
Available ESP32 devices:
1. MR Car 7 (A0:85:E3:F1:8C:C6)
2. CT Car 8 (B1:96:F4:G2:9D:D7)
Enter your choice (1-2):
```

#### Step 4: Monitor Progress
```
============================================================================
                           STARTING FIRMWARE TRANSFER
============================================================================
Target Device: A0:85:E3:F1:8C:C6
Firmware: fw_0_0_28_0_e42d49c7.bin
Size: 3686 KB
============================================================================

🔍 Scanning for ESP32 devices...
📱 Found device: MR Car 7 (A0:85:E3:F1:8C:C6)
🔗 Connecting to A0:85:E3:F1:8C:C6...
✅ Connected successfully
📤 Starting firmware transfer...
Progress: 10% (720/7205 chunks) - 46.2 KB/s
Progress: 20% (1441/7205 chunks) - 46.1 KB/s
...
Progress: 100% (7205/7205 chunks) - 46.0 KB/s
🎉 OTA transfer completed successfully!
Total time: 87.3 seconds
ESP32 has restarted with new firmware.
```

### Method 2: Direct Python Command

#### Basic Usage
```bash
python nimble_ota_client.py firmware.bin
```

#### With Specific Device Address
```bash
python nimble_ota_client.py firmware.bin --address "A0:85:E3:F1:8C:C6"
```

#### With Verbose Logging
```bash
python nimble_ota_client.py firmware.bin --verbose
```

#### With Device Name
```bash
python nimble_ota_client.py firmware.bin --name "MR Car 7"
```

### Method 3: Programmatic Usage

```python
import asyncio
from nimble_ota_client import NimBLEOTAClient

async def perform_ota():
    # Create client (auto-discovers devices)
    client = NimBLEOTAClient(verbose=True)
    
    try:
        # Connect to device
        if await client.connect():
            # Transfer firmware
            success = await client.transfer_firmware("firmware.bin")
            if success:
                print("✅ OTA completed successfully!")
            else:
                print("❌ OTA failed")
    finally:
        await client.disconnect()

# Run OTA
asyncio.run(perform_ota())
```

## 🔧 Advanced Usage

### Device Management

#### Scan for Available Devices
```bash
python nimble_ota_client.py --scan
```

#### Check Device Status
```bash
python nimble_ota_client.py --status --address "A0:85:E3:F1:8C:C6"
```

#### Abort Ongoing Transfer
```bash
python nimble_ota_client.py --abort --address "A0:85:E3:F1:8C:C6"
```

### Security Options

#### Enable BLE Security (Optional)
```bash
python nimble_ota_client.py firmware.bin --security
```

#### Bypass Security (Default)
```bash
python nimble_ota_client.py firmware.bin
```

### Performance Tuning

#### Connection Parameters
The system automatically optimizes:
- **Connection Interval:** 7.5-15ms
- **Supervision Timeout:** 20 seconds
- **MTU Size:** 517 bytes
- **Chunk Size:** 500 bytes

#### Transfer Modes
- **Default:** ACK waiting enabled (reliable, 46KB/s)
- **Pipeline:** ACK waiting disabled (faster, less reliable)

## 🔍 Troubleshooting

### Common Issues and Solutions

#### 1. Device Not Found
**Problem:** "No target devices found"
```bash
🔍 Scanning for ESP32 devices...
❌ No target devices found
```

**Solutions:**
- Verify ESP32 is powered on
- Check BLE advertising is active
- Ensure device is within range (< 10 meters)
- Restart ESP32 if needed

#### 2. Connection Failed
**Problem:** "Failed to connect to device"
```bash
🔗 Connecting to A0:85:E3:F1:8C:C6...
❌ Connection attempt 1 failed: [WinError 10060]
```

**Solutions:**
- Check Bluetooth adapter is enabled
- Verify MAC address is correct
- Try connecting from closer distance
- Restart Bluetooth service on computer

#### 3. Transfer Timeout
**Problem:** Transfer stops or times out
```bash
📤 Chunk 1500/7205 sent
⚠️  Supervision timeout - attempting reconnection...
```

**Solutions:**
- Ensure stable power supply to ESP32
- Check for interference (WiFi, other BLE devices)
- Verify BLE configuration on ESP32
- Try reducing distance between devices

#### 4. Verification Failed
**Problem:** "Image verification failed"
```bash
❌ OTA transfer failed: Image verification failed
```

**Solutions:**
- Verify firmware file integrity
- Check firmware is built for correct ESP32 variant
- Ensure firmware size is within partition limits
- Try rebuilding firmware

#### 5. Same Version Detected
**Problem:** "Same firmware version detected"
```bash
⚠️  Same firmware version detected - skipping update
```

**Solutions:**
- This is normal behavior (prevents unnecessary updates)
- Use `--force` flag to override (if implemented)
- Update version string in firmware

### Debug Mode

#### Enable Verbose Logging
```bash
python nimble_ota_client.py firmware.bin --verbose
```

#### ESP32 Debug Logs
Monitor ESP32 output during transfer:
```bash
idf.py monitor
```

**Key Log Messages:**
```
I (xxx) ota: Progress: 10% (720/7205 chunks)
I (xxx) ota: OTA update completed successfully
I (xxx) ota: Restarting system...
```

### Performance Diagnostics

#### Check Transfer Speed
Normal transfer speeds:
- **Excellent:** 45-50 KB/s
- **Good:** 35-45 KB/s
- **Poor:** <35 KB/s

#### Optimize Performance
1. **Ensure stable BLE connection**
2. **Minimize interference**
3. **Use optimal distance (1-5 meters)**
4. **Verify BLE configuration**

## ✅ Best Practices

### Before Starting OTA

1. **Verify Power Supply:** Ensure stable power during update
2. **Check Firmware:** Validate firmware file integrity
3. **Test Connection:** Verify BLE connectivity
4. **Backup Current Firmware:** Save current version if needed

### During OTA Transfer

1. **Maintain Proximity:** Keep devices within 5 meters
2. **Avoid Interference:** Minimize WiFi/BLE interference
3. **Monitor Progress:** Watch for error messages
4. **Don't Interrupt:** Allow transfer to complete

### After OTA Completion

1. **Verify Boot:** Confirm ESP32 boots with new firmware
2. **Test Functionality:** Verify all features work correctly
3. **Check Version:** Confirm firmware version updated
4. **Document Update:** Record successful update

### Security Considerations

1. **Firmware Validation:** Only use trusted firmware files
2. **Network Security:** Use secure network for file transfer
3. **Access Control:** Limit physical access during update
4. **Version Control:** Maintain firmware version history

## 📞 Support

### Getting Help

1. **Check Logs:** Review ESP32 and Python client logs
2. **Verify Setup:** Confirm all prerequisites are met
3. **Test Basic Connectivity:** Use scan function to verify BLE
4. **Consult Documentation:** Review technical documentation

### Common Commands Reference

```bash
# Scan for devices
python nimble_ota_client.py --scan

# Basic OTA transfer
python nimble_ota_client.py firmware.bin

# OTA with specific device
python nimble_ota_client.py firmware.bin --address "MAC_ADDR"

# OTA with verbose logging
python nimble_ota_client.py firmware.bin --verbose

# Check device status
python nimble_ota_client.py --status --address "MAC_ADDR"

# Batch file for easy selection
Ota_flash_firmware_select.bat
```

---

**Document Information:**
- **Version:** 2.0
- **Last Updated:** July 31, 2025
- **Support:** Development Team
