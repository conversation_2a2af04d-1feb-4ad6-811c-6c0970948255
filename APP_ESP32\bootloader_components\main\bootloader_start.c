/*
 * SPDX-FileCopyrightText: 2015-2021 Espressif Systems (Shanghai) CO LTD
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#include "bootloader_utility.h"
#include "bootloader_common.h"

// Function declarations
extern void bootloader_init(void);
extern void bootloader_reset(void);

/**
 * @brief Find factory partition index in bootloader state
 */
static int find_factory_partition_index(const bootloader_state_t* bs)
{
    if (!bs) {
        return INVALID_INDEX;
    }

    // Factory partition is typically the first partition or has the lowest offset
    int factory_candidate = INVALID_INDEX;
    uint32_t lowest_offset = 0xFFFFFFFF;

    for (int i = 0; i < bs->app_count; i++) {
        if (bs->ota[i].offset != 0 && bs->ota[i].size > 0) {
            // Factory partition typically has the lowest offset
            if (bs->ota[i].offset < lowest_offset) {
                lowest_offset = bs->ota[i].offset;
                factory_candidate = i;
            }
        }
    }

    return factory_candidate;
}

/**
 * @brief Enhanced bootloader main function with fallback logic
 */
void __attribute__((noreturn)) call_start_cpu0(void)
{
    // (1. Hardware initialization)
    bootloader_init();

    // (2. Load partition table)
    bootloader_state_t bs = {0};
    if (!bootloader_utility_load_partition_table(&bs)) {
        bootloader_reset();
    }

    // (3. Get selected boot partition)
    int boot_index = bootloader_utility_get_selected_boot_partition(&bs);

    if (boot_index == INVALID_INDEX) {
        // Try factory partition as fallback
        boot_index = find_factory_partition_index(&bs);

        if (boot_index == INVALID_INDEX) {
            bootloader_reset();
        }
    }

    // (4. Try to boot the selected partition)
    bootloader_utility_load_boot_image(&bs, boot_index);

    // Should never reach here - if we do, there was a boot failure
    // Try factory partition as fallback
    int factory_index = find_factory_partition_index(&bs);
    if (factory_index != INVALID_INDEX && factory_index != boot_index) {
        bootloader_utility_load_boot_image(&bs, factory_index);
    }

    // If we reach here, both partitions failed
    bootloader_reset();
}