20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 1
================================================================================
32 (System: 122) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/wpa_supplicant/src/ap/pmksa_cache_auth.c:425 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2:Er
ror) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 10
has incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
33 (System: 123) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/wpa_supplicant/src/ap/pmksa_cache_auth.c:425 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2:Er
ror) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 5 h
as incompatible type ’uint8_t’
Current status ’Analyze’
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 4
================================================================================
34 (System: 124) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/wpa_supplicant/src/ap/pmksa_cache_auth.c:425 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2:Er
ror) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 6 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
35 (System: 125) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/wpa_supplicant/src/ap/pmksa_cache_auth.c:425 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2:Er
ror) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 7 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
36 (System: 126) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/wpa_supplicant/src/ap/pmksa_cache_auth.c:425 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2:Er
ror) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 8 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
37 (System: 127) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/wpa_supplicant/src/ap/pmksa_cache_auth.c:425 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2:Er
ror) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 9 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
139 (System: 10) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos/portmacro.h:552 FUNCRET.IMPLI
CIT (2:Error) Analyze
Function implicitly returning int does not return value
Current status ’Analyze’
================================================================================
140 (System: 11) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos/portmacro.h:561 FUNCRET.IMPLI
CIT (2:Error) Analyze
Function implicitly returning int does not return value
Current status ’Analyze’
================================================================================
141 (System: 12) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos/portmacro.h:568 FUNCRET.IMPLI
CIT (2:Error) Analyze
Function implicitly returning int does not return value
Current status ’Analyze’
================================================================================
142 (System: 13) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos/portmacro.h:573 FUNCRET.IMPLI
CIT (2:Error) Analyze
Function implicitly returning int does not return value
Current status ’Analyze’
================================================================================
143 (System: 14) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos/portmacro.h:589 FUNCRET.IMPLI
CIT (2:Error) Analyze
Function implicitly returning int does not return value
Current status ’Analyze’
================================================================================
144 (System: 15) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos/portmacro.h:598 FUNCRET.IMPLI
CIT (2:Error) Analyze
Function implicitly returning int does not return value
Current status ’Analyze’
================================================================================
170 (System: 1028) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_hs_hci_evt.c:489 CWARN.MEM.NONPOD (4:Review
) Analyze
Memory manipulation routine ’memset’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
171 (System: 1029) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_hs_hci_evt.c:555 CWARN.MEM.NONPOD (4:Review
) Analyze
Memory manipulation routine ’memset’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
201 (System: 681) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/nimble/host/src/ble_att_svr.c:1132 UNINIT.STACK.MUST (1:Critical
) Analyze
’ha’ is used uninitialized in this function.
* ble_att_svr.c:1116: ’ha’ is declared
* ble_att_svr.c:1132: ’ha’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
202 (System: 682) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/nimble/host/src/ble_att_svr.c:2563 UNINIT.STACK.MUST (1:Critical
) Analyze
’entry’ is used uninitialized in this function.
* ble_att_svr.c:2559: ’entry’ is declared
* ble_att_svr.c:2563: ’entry’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
203 (System: 683) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 21
ents/bt/host/nimble/nimble/nimble/host/src/ble_att_svr.c:2604 UNINIT.STACK.MUST (1:Critical
) Analyze
’entry’ is used uninitialized in this function.
* ble_att_svr.c:2596: ’entry’ is declared
* ble_att_svr.c:2604: ’entry’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
236 (System: 1104) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_hs_startup.c:472 CWARN.MEM.NONPOD (4:Review
) Analyze
Memory manipulation routine ’memset’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
238 (System: 1052) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_hs_hci_util.c:85 CWARN.MEM.NONPOD (4:Review
) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 25
Current status ’Analyze’
================================================================================
239 (System: 1053) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_hs_hci_util.c:264 CWARN.MEM.NONPOD (4:Revie
w) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
281 (System: 1488) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bootloader_support/bootloader_flash/include/bootloader_flash_override.h:28 FUNCRET.GE
N (1:Critical) Analyze
Non-void function does not return value
Current status ’Analyze’
================================================================================
297 (System: 968) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/nimble/host/src/ble_gatts_lcl.c:111 SV.FMT_STR.PRINT_FORMAT_MISM
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 31
ATCH.BAD (2:Error) Analyze
printf format specification ’%d’ expects type ’int’ for ’d’, but parameter 4 has incompatib
le type ’uint16_t’
Current status ’Analyze’
================================================================================
298 (System: 969) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/nimble/host/src/ble_gatts_lcl.c:113 SV.FMT_STR.PRINT_FORMAT_MISM
ATCH.BAD (2:Error) Analyze
printf format specification ’%d’ expects type ’int’ for ’d’, but parameter 4 has incompatib
le type ’uint16_t’
Current status ’Analyze’
================================================================================
299 (System: 970) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/nimble/host/src/ble_gatts_lcl.c:115 SV.FMT_STR.PRINT_FORMAT_MISM
ATCH.BAD (2:Error) Analyze
printf format specification ’%d’ expects type ’int’ for ’d’, but parameter 4 has incompatib
le type ’uint8_t’
Current status ’Analyze’
================================================================================
300 (System: 971) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/nimble/host/src/ble_gatts_lcl.c:129 SV.FMT_STR.PRINT_FORMAT_MISM
ATCH.BAD (2:Error) Analyze
printf format specification ’%d’ expects type ’int’ for ’d’, but parameter 4 has incompatib
le type ’uint16_t’
Current status ’Analyze’
================================================================================
301 (System: 972) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/nimble/host/src/ble_gatts_lcl.c:145 SV.FMT_STR.PRINT_FORMAT_MISM
ATCH.BAD (2:Error) Analyze
printf format specification ’%d’ expects type ’int’ for ’d’, but parameter 4 has incompatib
le type ’uint16_t’
Current status ’Analyze’
================================================================================
302 (System: 973) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/nimble/host/src/ble_gatts_lcl.c:147 SV.FMT_STR.PRINT_FORMAT_MISM
ATCH.BAD (2:Error) Analyze
printf format specification ’%d’ expects type ’int’ for ’d’, but parameter 4 has incompatib
le type ’uint8_t’
Current status ’Analyze’
================================================================================
303 (System: 974) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/nimble/host/src/ble_gatts_lcl.c:170 SV.FMT_STR.PRINT_FORMAT_MISM
ATCH.BAD (2:Error) Analyze
printf format specification ’%d’ expects type ’int’ for ’d’, but parameter 4 has incompatib
le type ’uint16_t’
Current status ’Analyze’
================================================================================
304 (System: 975) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/nimble/host/src/ble_gatts_lcl.c:192 SV.FMT_STR.PRINT_FORMAT_MISM
ATCH.BAD (2:Error) Analyze
printf format specification ’%d’ expects type ’int’ for ’d’, but parameter 4 has incompatib
le type ’uint16_t’
Current status ’Analyze’
================================================================================
305 (System: 976) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/nimble/host/src/ble_gatts_lcl.c:195 SV.FMT_STR.PRINT_FORMAT_MISM
ATCH.BAD (2:Error) Analyze
printf format specification ’%d’ expects type ’int’ for ’d’, but parameter 4 has incompatib
le type ’uint16_t’
Current status ’Analyze’
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 32
================================================================================
332 (System: 1873) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c:93 CWARN.MEM.NONPOD
(4:Review) Analyze
Memory manipulation routine ’memset’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
333 (System: 1874) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c:272 CWARN.MEM.NONPOD
(4:Review) Analyze
Memory manipulation routine ’memset’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
474 (System: 661) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/porting/npl/freertos/include/nimble/nimble_npl_os.h:155 VOIDRET
(2:Error) Analyze
void function returns value
Current status ’Analyze’
================================================================================
475 (System: 662) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/porting/npl/freertos/include/nimble/nimble_npl_os.h:161 VOIDRET
(2:Error) Analyze
void function returns value
Current status ’Analyze’
================================================================================
476 (System: 663) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/porting/npl/freertos/include/nimble/nimble_npl_os.h:173 VOIDRET
(2:Error) Analyze
void function returns value
Current status ’Analyze’
================================================================================
477 (System: 664) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/porting/npl/freertos/include/nimble/nimble_npl_os.h:179 VOIDRET
(2:Error) Analyze
void function returns value
Current status ’Analyze’
================================================================================
478 (System: 665) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/porting/npl/freertos/include/nimble/nimble_npl_os.h:185 VOIDRET
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 52
(2:Error) Analyze
void function returns value
Current status ’Analyze’
================================================================================
479 (System: 666) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/porting/npl/freertos/include/nimble/nimble_npl_os.h:198 VOIDRET
(2:Error) Analyze
void function returns value
Current status ’Analyze’
================================================================================
480 (System: 667) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/porting/npl/freertos/include/nimble/nimble_npl_os.h:216 VOIDRET
(2:Error) Analyze
void function returns value
Current status ’Analyze’
================================================================================
481 (System: 668) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/porting/npl/freertos/include/nimble/nimble_npl_os.h:282 VOIDRET
(2:Error) Analyze
void function returns value
Current status ’Analyze’
================================================================================
482 (System: 669) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/porting/npl/freertos/include/nimble/nimble_npl_os.h:294 VOIDRET
(2:Error) Analyze
void function returns value
Current status ’Analyze’
================================================================================
483 (System: 670) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/porting/npl/freertos/include/nimble/nimble_npl_os.h:319 VOIDRET
(2:Error) Analyze
void function returns value
Current status ’Analyze’
================================================================================
484 (System: 671) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/porting/npl/freertos/include/nimble/nimble_npl_os.h:355 VOIDRET
(2:Error) Analyze
void function returns value
Current status ’Analyze’
================================================================================
485 (System: 672) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/bt/host/nimble/nimble/porting/npl/freertos/include/nimble/nimble_npl_os.h:375 VOIDRET
(2:Error) Analyze
void function returns value
Current status ’Analyze’
================================================================================
658 (System: 1767) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/fastpbkdf2.c:366 UNINIT.STACK.ARRAY.MIGHT (1
:Critical) Analyze
’k’ array elements might be used uninitialized in this function.
* fastpbkdf2.c:366: ’*k’ is declared.
* fastpbkdf2.c:366: nkey>64 is false
* fastpbkdf2.c:366: k!=key is false
* fastpbkdf2.c:367: 64>nkey is false
* fastpbkdf2.c:366: ’k’ array elements are used uninitialized.
Current status ’Analyze’
================================================================================
681 (System: 1385) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 10 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
682 (System: 1386) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 11 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
683 (System: 1387) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 76
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 12 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
684 (System: 1388) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 13 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
685 (System: 1389) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 14 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
686 (System: 1390) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 15 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
687 (System: 1391) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 16 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
688 (System: 1392) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 17 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
689 (System: 1393) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 18 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
690 (System: 1394) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 3 ha
s incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
691 (System: 1395) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 4 ha
s incompatible type ’uint8_t’
Current status ’Analyze’
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 77
================================================================================
692 (System: 1396) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 5 ha
s incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
693 (System: 1397) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 6 ha
s incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
694 (System: 1398) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 7 ha
s incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
695 (System: 1399) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 8 ha
s incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
696 (System: 1400) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/src/ble_uuid.c:174 SV.FMT_STR.PRINT_FORMAT_MISMATCH
.BAD (2:Error) Analyze
sprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 9 ha
s incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
712 (System: 1321) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/services/ans/src/ble_svc_ans.c:379 NPD.GEN.MUST (1:
Critical) Analyze
Null pointer ’ble_svc_ans_new_alert_val’ that comes from line 375 will be dereferenced at l
ine 379.
* ble_svc_ans.c:375: ’ble_svc_ans_new_alert_val’ is assigned a constant NULL value within
function ’memset’.
* ble_svc_ans.c:379: ’ble_svc_ans_new_alert_val’ is explicitly dereferenced.
Current status ’Analyze’
================================================================================
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 80
717 (System: 1466) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/porting/npl/freertos/src/npl_os_freertos.c:1139 NPD.CHECK.MUST
(1:Critical) Analyze
Pointer ’npl_funcs’ will be dereferenced at line 1139 after having been checked for NULL.
* npl_os_freertos.c:1135: ’npl_funcs’ is checked for NULL.
* npl_os_freertos.c:1135: !npl_funcs is true
* npl_os_freertos.c:1139: ’npl_funcs’ is dereferenced by passing argument 1 to function ’
memcpy’.
Current status ’Analyze’
================================================================================
718 (System: 1467) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/porting/npl/freertos/src/npl_os_freertos.c:1139 NPD.FUNC.MUST (
1:Critical) Analyze
Pointer ’npl_funcs’ returned from call to function ’malloc’ at line 1134 may be NULL and wi
ll be dereferenced at line 1139.
* npl_os_freertos.c:1134: ’npl_funcs’ is assigned the return value from function ’malloc’
.
* npl_os_freertos.c:1135: !npl_funcs is true
* npl_os_freertos.c:1139: ’npl_funcs’ is dereferenced by passing argument 1 to function ’
memcpy’.
Current status ’Analyze’
================================================================================
732 (System: 1356) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/services/prox/src/ble_svc_prox.c:114 INFINITE_LOOP.
LOCAL (2:Error) Analyze
Infinite loop
* ble_svc_prox.c:114: Entering loop
* ble_svc_prox.c:117: condition 1<=3 is always true
* ble_svc_prox.c:117: condition (3>= (ESP_LOG_INFO) ) is always true
* ble_svc_prox.c:117: condition ESP_LOG_INFO==ESP_LOG_ERROR is always false
* ble_svc_prox.c:117: condition ESP_LOG_INFO==ESP_LOG_WARN is always false
* ble_svc_prox.c:117: condition ESP_LOG_INFO==ESP_LOG_DEBUG is always false
* ble_svc_prox.c:117: condition ESP_LOG_INFO==ESP_LOG_VERBOSE is always false
* ble_svc_prox.c:117: condition 0 is always false
* ble_svc_prox.c:117: condition 0 is always false
* ble_svc_prox.c:117: condition 0 is always false
Current status ’Analyze’
================================================================================
734 (System: 1311) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/store/ram/src/ble_store_ram.c:210 CWARN.MEM.NONPOD
(4:Review) Analyze
Memory manipulation routine ’memmove’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
770 (System: 285) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/wpa_supplicant/src/eap_peer/eap_fast_pac.c:525 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2
:Error) Analyze
snprintf format specification ’%c’ expects type ’int’ for ’c’, but parameter 4 has incompat
ible type ’uint8_t’
Current status ’Analyze’
================================================================================
771 (System: 286) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/wpa_supplicant/src/eap_peer/eap_fast_pac.c:583 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2
:Error) Analyze
snprintf format specification ’%d’ expects type ’int’ for ’d’, but parameter 4 has incompat
ible type ’uint16_t’
Current status ’Analyze’
================================================================================
822 (System: 1882) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c:635 CWARN.MEM.NONPOD (4:
Review) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
823 (System: 1883) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c:1210 CWARN.MEM.NONPOD (4
:Review) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
824 (System: 1884) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c:1211 CWARN.MEM.NONPOD (4
:Review) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
825 (System: 1885) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c:133 NPD.FUNC.MIGHT (1:Cr
itical) Analyze
Pointer ’curve’ returned from call to function ’mbedtls_ecp_curve_info_from_name’ at line 1
24 may be NULL and may be dereferenced at line 133.
* crypto_mbedtls-ec.c:124: ’curve’ is assigned the return value from function ’mbedtls_ec
p_curve_info_from_name’.
* ecp.c:469: ’mbedtls_ecp_curve_info_from_name’ explicitly returns a NULL value.
* crypto_mbedtls-ec.c:133: ’curve’ is explicitly dereferenced.
Current status ’Analyze’
================================================================================
826 (System: 1886) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c:264 UNINIT.STACK.MUST (1
:Critical) Analyze
’entropy.mbedtls_entropy_context::source’ is used uninitialized in this function.
* crypto_mbedtls-ec.c:261: ’entropy.mbedtls_entropy_context::source’ is declared
* crypto_mbedtls-ec.c:264: ’entropy.mbedtls_entropy_context::source’ is used, but is unin
itialized.
Current status ’Analyze’
================================================================================
827 (System: 1887) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c:462 UNINIT.STACK.MUST (1
:Critical) Analyze
’entropy.mbedtls_entropy_context::source’ is used uninitialized in this function.
* crypto_mbedtls-ec.c:459: ’entropy.mbedtls_entropy_context::source’ is declared
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 98
* crypto_mbedtls-ec.c:462: ’entropy.mbedtls_entropy_context::source’ is used, but is unin
itialized.
Current status ’Analyze’
================================================================================
828 (System: 1888) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c:734 UNINIT.STACK.MUST (1
:Critical) Analyze
’entropy.mbedtls_entropy_context::source’ is used uninitialized in this function.
* crypto_mbedtls-ec.c:730: ’entropy.mbedtls_entropy_context::source’ is declared
* crypto_mbedtls-ec.c:734: ’entropy.mbedtls_entropy_context::source’ is used, but is unin
itialized.
Current status ’Analyze’
================================================================================
829 (System: 1889) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c:1105 UNINIT.STACK.MUST (
1:Critical) Analyze
’entropy.mbedtls_entropy_context::source’ is used uninitialized in this function.
* crypto_mbedtls-ec.c:1085: ’entropy.mbedtls_entropy_context::source’ is declared
* crypto_mbedtls-ec.c:1105: ’entropy.mbedtls_entropy_context::source’ is used, but is uni
nitialized.
Current status ’Analyze’
================================================================================
830 (System: 1890) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c:1176 UNINIT.STACK.MUST (
1:Critical) Analyze
’entropy.mbedtls_entropy_context::source’ is used uninitialized in this function.
* crypto_mbedtls-ec.c:1172: ’entropy.mbedtls_entropy_context::source’ is declared.
* crypto_mbedtls-ec.c:1176: ’entropy.mbedtls_entropy_context::source’ is used, but is uni
nitialized.
Current status ’Analyze’
================================================================================
831 (System: 16) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c:411 CWARN.MEM.NONPOD (4:R
eview) Analyze
Memory manipulation routine ’memmove’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
832 (System: 17) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c:259 UNINIT.HEAP.MUST (1:C
ritical) Analyze
’entropy->mbedtls_entropy_context::source’ gets its value from uninitialized heap memory ar
ea.
* crypto_mbedtls-rsa.c:246: ’entropy->mbedtls_entropy_context::source’ memory is allocate
d through call to ’malloc’
* crypto_mbedtls-rsa.c:259: ’entropy->mbedtls_entropy_context::source’ is used, but is un
initialized.
Current status ’Analyze’
================================================================================
833 (System: 18) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compone
nts/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c:303 UNINIT.HEAP.MUST (1:C
ritical) Analyze
’entropy->mbedtls_entropy_context::source’ gets its value from uninitialized heap memory ar
ea.
* crypto_mbedtls-rsa.c:290: ’entropy->mbedtls_entropy_context::source’ memory is allocate
d through call to ’malloc’
* crypto_mbedtls-rsa.c:303: ’entropy->mbedtls_entropy_context::source’ is used, but is un
initialized.
Current status ’Analyze’
================================================================================
841 (System: 204) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/protocomm/src/transports/protocomm_console.c:161 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD
(2:Error) Analyze
printf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 2 has
incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
842 (System: 205) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/protocomm/src/transports/protocomm_console.c:49 SV.FMT_STR.SCAN_FORMAT_MISMATCH.BAD (2
:Error) Analyze
sscanf format specification ’%2hhx’ expects type ’unsigned char *’ for ’x’, but parameter 3
has incompatible type ’uint8_t*’
Current status ’Analyze’
================================================================================
995 (System: 1293) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:232 ABV.GENERAL (1
:Critical) Analyze
Array ’key_string’ of size 16 may use index value(s) 16..19
* ble_store_nvs.c:227: Array ’key_string’ size is 16.
* ble_store_nvs.c:232: Possible parameter values: obj_type == 1.
* ble_store_nvs.c:232: ’key_string’ is passed as an argument to function ’get_nvs_key_str
ing’.
* ble_store_nvs.c:68: ’sprintf’ is called.
Current status ’Analyze’
================================================================================
996 (System: 1294) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:232 ABV.GENERAL (1
:Critical) Analyze
Array ’key_string’ of size 16 may use index value(s) 16..20
* ble_store_nvs.c:227: Array ’key_string’ size is 16.
* ble_store_nvs.c:232: Possible parameter values: obj_type == 2.
* ble_store_nvs.c:232: ’key_string’ is passed as an argument to function ’get_nvs_key_str
ing’.
* ble_store_nvs.c:66: ’sprintf’ is called.
Current status ’Analyze’
================================================================================
997 (System: 1295) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:232 ABV.GENERAL (1
:Critical) Analyze
Array ’key_string’ of size 16 may use index value(s) 16..21
* ble_store_nvs.c:227: Array ’key_string’ size is 16.
* ble_store_nvs.c:232: Possible parameter values: obj_type == 4.
* ble_store_nvs.c:232: ’key_string’ is passed as an argument to function ’get_nvs_key_str
ing’.
* ble_store_nvs.c:63: ’sprintf’ is called.
Current status ’Analyze’
================================================================================
998 (System: 1296) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:327 ABV.GENERAL (1
:Critical) Analyze
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 129
Array ’key_string’ of size 16 may use index value(s) 16..19
* ble_store_nvs.c:314: Array ’key_string’ size is 16.
* ble_store_nvs.c:327: Possible parameter values: obj_type == 1, index <= 8.
* ble_store_nvs.c:327: ’key_string’ is passed as an argument to function ’get_nvs_key_str
ing’.
* ble_store_nvs.c:68: ’sprintf’ is called.
Current status ’Analyze’
================================================================================
999 (System: 1297) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:327 ABV.GENERAL (1
:Critical) Analyze
Array ’key_string’ of size 16 may use index value(s) 16..20
* ble_store_nvs.c:314: Array ’key_string’ size is 16.
* ble_store_nvs.c:327: Possible parameter values: obj_type == 2, index <= 8.
* ble_store_nvs.c:327: ’key_string’ is passed as an argument to function ’get_nvs_key_str
ing’.
* ble_store_nvs.c:66: ’sprintf’ is called.
Current status ’Analyze’
================================================================================
1000 (System: 1298) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:327 ABV.GENERAL (
1:Critical) Analyze
Array ’key_string’ of size 16 may use index value(s) 16..21
* ble_store_nvs.c:314: Array ’key_string’ size is 16.
* ble_store_nvs.c:327: Possible parameter values: obj_type == 4, index <= 8.
* ble_store_nvs.c:327: ’key_string’ is passed as an argument to function ’get_nvs_key_str
ing’.
* ble_store_nvs.c:63: ’sprintf’ is called.
Current status ’Analyze’
================================================================================
1001 (System: 1299) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:401 ABV.GENERAL (
1:Critical) Analyze
Array ’key_string’ of size 16 may use index value(s) 16..19
* ble_store_nvs.c:386: Array ’key_string’ size is 16.
* ble_store_nvs.c:401: Possible parameter values: obj_type == 1.
* ble_store_nvs.c:401: ’key_string’ is passed as an argument to function ’get_nvs_key_str
ing’.
* ble_store_nvs.c:68: ’sprintf’ is called.
Current status ’Analyze’
================================================================================
1002 (System: 1300) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:401 ABV.GENERAL (
1:Critical) Analyze
Array ’key_string’ of size 16 may use index value(s) 16..20
* ble_store_nvs.c:386: Array ’key_string’ size is 16.
* ble_store_nvs.c:401: Possible parameter values: obj_type == 2.
* ble_store_nvs.c:401: ’key_string’ is passed as an argument to function ’get_nvs_key_str
ing’.
* ble_store_nvs.c:66: ’sprintf’ is called.
Current status ’Analyze’
================================================================================
1003 (System: 1301) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:401 ABV.GENERAL (
1:Critical) Analyze
Array ’key_string’ of size 16 may use index value(s) 16..21
* ble_store_nvs.c:386: Array ’key_string’ size is 16.
* ble_store_nvs.c:401: Possible parameter values: obj_type == 4.
* ble_store_nvs.c:401: ’key_string’ is passed as an argument to function ’get_nvs_key_str
ing’.
* ble_store_nvs.c:63: ’sprintf’ is called.
Current status ’Analyze’
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 130
================================================================================
1004 (System: 1302) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:469 ABV.GENERAL (
1:Critical) Analyze
Array ’key_string’ of size 16 may use index value(s) 16..19
* ble_store_nvs.c:466: Array ’key_string’ size is 16.
* ble_store_nvs.c:469: Possible parameter values: obj_type == 1.
* ble_store_nvs.c:469: ’key_string’ is passed as an argument to function ’get_nvs_key_str
ing’.
* ble_store_nvs.c:68: ’sprintf’ is called.
Current status ’Analyze’
================================================================================
1005 (System: 1303) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:469 ABV.GENERAL (
1:Critical) Analyze
Array ’key_string’ of size 16 may use index value(s) 16..20
* ble_store_nvs.c:466: Array ’key_string’ size is 16.
* ble_store_nvs.c:469: Possible parameter values: obj_type == 2.
* ble_store_nvs.c:469: ’key_string’ is passed as an argument to function ’get_nvs_key_str
ing’.
* ble_store_nvs.c:66: ’sprintf’ is called.
Current status ’Analyze’
================================================================================
1006 (System: 1304) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:469 ABV.GENERAL (
1:Critical) Analyze
Array ’key_string’ of size 16 may use index value(s) 16..21
* ble_store_nvs.c:466: Array ’key_string’ size is 16.
* ble_store_nvs.c:469: Possible parameter values: obj_type == 4.
* ble_store_nvs.c:469: ’key_string’ is passed as an argument to function ’get_nvs_key_str
ing’.
* ble_store_nvs.c:63: ’sprintf’ is called.
Current status ’Analyze’
================================================================================
1007 (System: 1305) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:282 ABV.MEMBER (1
:Critical) Analyze
Array ’&(cur.cccd)’ of size 20 may use index value(s) 20..55
* ble_store_nvs.c:282: Possible parameter values: obj_type <= 2 [4,5] >= 9, value != 0, n
um_value >= 1.
* ble_store_nvs.c:282: Array ’&(cur.cccd)’ size is 1.
* ble_store_nvs.c:282: ’&(cur.cccd)’ is passed as an argument to function ’get_nvs_matchi
ng_index’.
* ble_store_nvs.c:98: ’memcmp’ is called.
* ble_store_nvs.c:282: Object ’&(cur.cccd)’ of type ’ble_store_value_cccd[1]’ is a buffer
of length 20 bytes.
Current status ’Analyze’
================================================================================
1008 (System: 1306) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:503 CWARN.MEM.NON
POD (4:Review) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1009 (System: 1307) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:508 CWARN.MEM.NON
POD (4:Review) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1010 (System: 1308) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:520 CWARN.MEM.NON
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 131
POD (4:Review) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1011 (System: 1309) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:526 CWARN.MEM.NON
POD (4:Review) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1012 (System: 1310) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_nvs.c:531 CWARN.MEM.NON
POD (4:Review) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1047 (System: 1317) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_store_util.c:118 CWARN.MEM.NONPOD (4:Revie
w) Analyze
Memory manipulation routine ’memset’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1048 (System: 1318) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_store_util.c:131 CWARN.MEM.NONPOD (4:Revie
w) Analyze
Memory manipulation routine ’memset’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1049 (System: 1319) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_store_util.c:149 CWARN.MEM.NONPOD (4:Revie
w) Analyze
Memory manipulation routine ’memset’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1050 (System: 1320) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_store_util.c:157 CWARN.MEM.NONPOD (4:Revie
w) Analyze
Memory manipulation routine ’memset’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1058 (System: 1676) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/wpa_supplicant/esp_supplicant/src/esp_wps.c:1244 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BA
D (2:Error) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 4 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
1059 (System: 1677) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/wpa_supplicant/esp_supplicant/src/esp_wps.c:1244 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BA
D (2:Error) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 5 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
1060 (System: 1678) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/wpa_supplicant/esp_supplicant/src/esp_wps.c:1244 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BA
D (2:Error) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 6 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 137
1061 (System: 1679) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/wpa_supplicant/esp_supplicant/src/esp_wps.c:1244 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BA
D (2:Error) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 7 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
1062 (System: 1680) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/wpa_supplicant/esp_supplicant/src/esp_wps.c:1244 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BA
D (2:Error) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 8 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
1063 (System: 1681) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/wpa_supplicant/esp_supplicant/src/esp_wps.c:1244 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BA
D (2:Error) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 9 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
1114 (System: 812) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/APP_ESP32/man
aged_components/joltwallet__littlefs/src/littlefs/lfs.c:1090 UNINIT.STACK.MIGHT (1:Critical
) Analyze
’res’ might be used uninitialized in this function.
* lfs.c:910: ’res’ is declared
* lfs.c:916: true is false
* lfs.c:1069: sp>0 is false
* lfs.c:1090: ’res’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1115 (System: 813) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/APP_ESP32/man
aged_components/joltwallet__littlefs/src/littlefs/lfs.c:1333 UNINIT.STACK.MIGHT (1:Critical
) Analyze
’fcrc.size’ might be used uninitialized in this function.
* lfs.c:1113: Entering loop, because i<2 is true
* lfs.c:1146: ’fcrc.size’ is declared.
* lfs.c:1152: Entering loop, because true is true
* lfs.c:1264: lfs_tag_type3(tag) ==LFS_TYPE_FCRC is false
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 148
* lfs.c:1333: ’fcrc.size’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1116 (System: 814) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/APP_ESP32/man
aged_components/joltwallet__littlefs/src/littlefs/lfs.c:1341 UNINIT.STACK.MIGHT (1:Critical
) Analyze
’fcrc.crc’ might be used uninitialized in this function.
* lfs.c:1113: Entering loop, because i<2 is true
* lfs.c:1146: ’fcrc.crc’ is declared.
* lfs.c:1152: Entering loop, because true is true
* lfs.c:1264: lfs_tag_type3(tag) ==LFS_TYPE_FCRC is false
* lfs.c:1341: ’fcrc.crc’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1160 (System: 1219) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/esp_local_ctrl/src/esp_local_ctrl.c:184 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2:Erro
r) Analyze
snprintf format specification ’%d’ expects type ’int’ for ’d’, but parameter 6 has incompat
ible type ’uint8_t’
Current status ’Analyze’
================================================================================
1161 (System: 1220) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/esp_local_ctrl/src/esp_local_ctrl.c:191 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2:Erro
r) Analyze
snprintf format specification ’%d’ expects type ’int’ for ’d’, but parameter 6 has incompat
ible type ’uint8_t’
Current status ’Analyze’
================================================================================
1172 (System: 115) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 155
nents/wpa_supplicant/src/rsn_supp/pmksa_cache.c:485 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2
:Error) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 10
has incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
1173 (System: 116) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/src/rsn_supp/pmksa_cache.c:485 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2
:Error) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 5 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
1174 (System: 117) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/src/rsn_supp/pmksa_cache.c:485 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2
:Error) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 6 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
1175 (System: 118) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/src/rsn_supp/pmksa_cache.c:485 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2
:Error) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 7 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
1176 (System: 119) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/src/rsn_supp/pmksa_cache.c:485 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2
:Error) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 8 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
1177 (System: 120) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/src/rsn_supp/pmksa_cache.c:485 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2
:Error) Analyze
snprintf format specification ’%02x’ expects type ’unsigned int’ for ’x’, but parameter 9 h
as incompatible type ’uint8_t’
Current status ’Analyze’
================================================================================
1214 (System: 1251) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_sm_alg.c:546 UNINIT.STACK.MUST (1:Critical
) Analyze
’entropy.mbedtls_entropy_context::source’ is used uninitialized in this function.
* ble_sm_alg.c:535: ’entropy.mbedtls_entropy_context::source’ is declared
* ble_sm_alg.c:546: ’entropy.mbedtls_entropy_context::source’ is used, but is uninitializ
ed.
Current status ’Analyze’
================================================================================
1215 (System: 1252) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_sm_alg.c:644 UNINIT.STACK.MUST (1:Critical
) Analyze
’entropy.mbedtls_entropy_context::source’ is used uninitialized in this function.
* ble_sm_alg.c:640: ’entropy.mbedtls_entropy_context::source’ is declared
* ble_sm_alg.c:644: ’entropy.mbedtls_entropy_context::source’ is used, but is uninitializ
ed.
Current status ’Analyze’
================================================================================
1216 (System: 1277) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/store/config/src/ble_store_config.c:326 CWARN.MEM.
NONPOD (4:Review) Analyze
Memory manipulation routine ’memmove’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1295 (System: 1763) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/porting/nimble/src/os_mempool.c:221 UNINIT.STACK.MUST (1:Criti
cal) Analyze
’cur’ is used uninitialized in this function.
* os_mempool.c:213: ’cur’ is declared
* os_mempool.c:221: ’cur’ is used, but is uninitialized.
Current status ’Analyze’
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 171
================================================================================
1296 (System: 1764) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/porting/nimble/src/os_mempool.c:303 UNINIT.STACK.MUST (1:Criti
cal) Analyze
’block’ is used uninitialized in this function.
* os_mempool.c:300: ’block’ is declared
* os_mempool.c:303: ’block’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1492 (System: 1264) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/wpa_supplicant/src/wps/wps_common.c:506 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2:Erro
r) Analyze
snprintf format specification ’%08X’ expects type ’unsigned int’ for ’X’, but parameter 5 h
as incompatible type ’uint32_t’
Current status ’Analyze’
================================================================================
1493 (System: 1265) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/wpa_supplicant/src/wps/wps_common.c:506 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2:Erro
r) Analyze
snprintf format specification ’%u’ expects type ’unsigned int’ for ’u’, but parameter 4 has
incompatible type ’uint16_t’
Current status ’Analyze’
================================================================================
1494 (System: 1266) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/wpa_supplicant/src/wps/wps_common.c:506 SV.FMT_STR.PRINT_FORMAT_MISMATCH.BAD (2:Erro
r) Analyze
snprintf format specification ’%u’ expects type ’unsigned int’ for ’u’, but parameter 6 has
incompatible type ’uint16_t’
Current status ’Analyze’
================================================================================
1507 (System: 23) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls.c:945 CWARN.MEM.NONPOD (4:Revi
ew) Analyze
Memory manipulation routine ’memmove’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1508 (System: 24) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compon
ents/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls.c:946 CWARN.MEM.NONPOD (4:Revi
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 212
ew) Analyze
Memory manipulation routine ’memset’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1580 (System: 840) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/examp
les/bluetooth/nimble/common/nimble_peripheral_utils/scli.c:35 SV.FMT_STR.BAD_SCAN_FORMAT (2
:Error) Analyze
Width is not specified for ’s’ conversion specifier. This can result in an overflow of the
buffer provided in argument 3 of a call to ’sscanf’
Current status ’Analyze’
================================================================================
1744 (System: 1009) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 241
onents/bt/host/nimble/nimble/nimble/host/src/ble_hs_conn.c:568 UNINIT.STACK.MIGHT (1:Critic
al) Analyze
’next_exp_in_new’ might be used uninitialized in this function.
* ble_hs_conn.c:511: ’next_exp_in_new’ is declared
* ble_hs_conn.c:541: time_diff<next_exp_in is false
* ble_hs_conn.c:554: time_diff<=0 is false
* ble_hs_conn.c:561: time_diff<next_exp_in is false
* ble_hs_conn.c:568: ’next_exp_in_new’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1745 (System: 1010) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_hs_conn.c:62 UNINIT.STACK.MUST (1:Critical
) Analyze
’chan’ is used uninitialized in this function.
* ble_hs_conn.c:60: ’chan’ is declared
* ble_hs_conn.c:62: ’chan’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1746 (System: 1011) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_hs_conn.c:83 UNINIT.STACK.MUST (1:Critical
) Analyze
’chan’ is used uninitialized in this function.
* ble_hs_conn.c:81: ’chan’ is declared
* ble_hs_conn.c:83: ’chan’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1747 (System: 1012) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_hs_conn.c:101 UNINIT.STACK.MUST (1:Critica
l) Analyze
’tmp’ is used uninitialized in this function.
* ble_hs_conn.c:99: ’tmp’ is declared
* ble_hs_conn.c:101: ’tmp’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1748 (System: 1013) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_hs_conn.c:121 UNINIT.STACK.MUST (1:Critica
l) Analyze
’cur’ is used uninitialized in this function.
* ble_hs_conn.c:118: ’cur’ is declared
* ble_hs_conn.c:121: ’cur’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1749 (System: 1014) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_hs_conn.c:226 UNINIT.STACK.MUST (1:Critica
l) Analyze
’conn’ is used uninitialized in this function.
* ble_hs_conn.c:222: ’conn’ is declared
* ble_hs_conn.c:226: ’conn’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1750 (System: 1015) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_hs_conn.c:304 UNINIT.STACK.MUST (1:Critica
l) Analyze
’conn’ is used uninitialized in this function.
* ble_hs_conn.c:300: ’conn’ is declared
* ble_hs_conn.c:304: ’conn’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1751 (System: 1016) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_hs_conn.c:340 UNINIT.STACK.MUST (1:Critica
l) Analyze
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 242
’conn’ is used uninitialized in this function.
* ble_hs_conn.c:331: ’conn’ is declared.
* ble_hs_conn.c:340: ’conn’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1753 (System: 1017) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_hs_conn.c:376 UNINIT.STACK.MUST (1:Critica
l) Analyze
’conn’ is used uninitialized in this function.
* ble_hs_conn.c:370: ’conn’ is declared
* ble_hs_conn.c:376: ’conn’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1754 (System: 1018) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_hs_conn.c:522 UNINIT.STACK.MUST (1:Critica
l) Analyze
’conn’ is used uninitialized in this function.
* ble_hs_conn.c:508: ’conn’ is declared
* ble_hs_conn.c:522: ’conn’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1756 (System: 1481) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c:64 FUNCRET
.IMPLICIT (2:Error) Analyze
Function implicitly returning int does not return value
Current status ’Analyze’
================================================================================
1757 (System: 1482) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c:87 FUNCRET
.IMPLICIT (2:Error) Analyze
Function implicitly returning int does not return value
Current status ’Analyze’
================================================================================
1758 (System: 1483) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c:93 FUNCRET
.IMPLICIT (2:Error) Analyze
Function implicitly returning int does not return value
Current status ’Analyze’
================================================================================
1759 (System: 1484) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c:100 FUNCRE
T.IMPLICIT (2:Error) Analyze
Function implicitly returning int does not return value
Current status ’Analyze’
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 243
================================================================================
1760 (System: 1485) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c:134 FUNCRE
T.IMPLICIT (2:Error) Analyze
Function implicitly returning int does not return value
Current status ’Analyze’
================================================================================
1761 (System: 1486) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32s3.c:264 FUNCRE
T.IMPLICIT (2:Error) Analyze
Function implicitly returning int does not return value
Current status ’Analyze’
================================================================================
1794 (System: 1436) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/porting/nimble/src/nimble_port.c:315 FUNCRET.IMPLICIT (2:Error
) Analyze
Function implicitly returning int does not return value
Current status ’Analyze’
================================================================================
1795 (System: 817) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/esp_driver_usb_serial_jtag/src/usb_serial_jtag_connection_monitor.c:72 FUNCRET.IMPLIC
IT (2:Error) Analyze
Function implicitly returning int does not return value
Current status ’Analyze’
================================================================================
1798 (System: 173) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/tls_mbedtls.c:1073 CWARN.MEM.NONPOD (4:Revie
w) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 248
Current status ’Analyze’
================================================================================
1799 (System: 174) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/tls_mbedtls.c:1074 CWARN.MEM.NONPOD (4:Revie
w) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1800 (System: 175) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/tls_mbedtls.c:1076 CWARN.MEM.NONPOD (4:Revie
w) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1801 (System: 176) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/wpa_supplicant/esp_supplicant/src/crypto/tls_mbedtls.c:1083 CWARN.MEM.NONPOD (4:Revie
w) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1832 (System: 639) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/common/btc/profile/esp/blufi/nimble_host/esp_blufi.c:130 CWARN.MEM.NONPOD (4:Revie
w) Analyze
Memory manipulation routine ’memset’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1833 (System: 640) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/common/btc/profile/esp/blufi/nimble_host/esp_blufi.c:132 CWARN.MEM.NONPOD (4:Revie
w) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1834 (System: 641) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/common/btc/profile/esp/blufi/nimble_host/esp_blufi.c:139 CWARN.MEM.NONPOD (4:Revie
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 254
w) Analyze
Memory manipulation routine ’memcpy’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1835 (System: 642) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/common/btc/profile/esp/blufi/nimble_host/esp_blufi.c:427 CWARN.MEM.NONPOD (4:Revie
w) Analyze
Memory manipulation routine ’memset’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1836 (System: 643) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/common/btc/profile/esp/blufi/nimble_host/esp_blufi.c:485 CWARN.MEM.NONPOD (4:Revie
w) Analyze
Memory manipulation routine ’memset’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1837 (System: 644) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/bt/common/btc/profile/esp/blufi/nimble_host/esp_blufi.c:130 NPD.FUNC.MUST (1:Critical
) Analyze
Pointer ’fw_buf’ returned from call to function ’malloc’ at line 129 may be NULL and will b
e dereferenced at line 130.
* esp_blufi.c:129: ’fw_buf’ is assigned the return value from function ’malloc’.
* esp_blufi.c:130: ’fw_buf’ is dereferenced by passing argument 1 to function ’memset’.
Current status ’Analyze’
================================================================================
1838 (Local) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/components/
bt/common/btc/profile/esp/blufi/nimble_host/esp_blufi.c:325 UNINIT.STACK.ARRAY.MUST (1:Crit
ical) Analyze
’desc.peer_id_addr.val’ array elements are used uninitialized in this function.
* esp_blufi.c:304: ’*(desc.peer_id_addr.val)’ is declared.
* esp_blufi.c:323: passing ’&desc’ to ’ble_gap_conn_find’ does not initialize ’*(desc.pee
r_id_addr.val)’
* esp_blufi.c:325: ’desc.peer_id_addr.val’ array elements are used uninitialized.
Current status ’Analyze’
================================================================================
1839 (Local) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/components/
bt/common/btc/profile/esp/blufi/nimble_host/esp_blufi.c:563 UNINIT.STACK.ARRAY.MUST (1:Crit
ical) Analyze
’desc.peer_id_addr.val’ array elements are used uninitialized in this function.
* esp_blufi.c:543: ’*(desc.peer_id_addr.val)’ is declared.
* esp_blufi.c:561: passing ’&desc’ to ’ble_gap_conn_find’ does not initialize ’*(desc.pee
r_id_addr.val)’
* esp_blufi.c:563: ’desc.peer_id_addr.val’ array elements are used uninitialized.
Current status ’Analyze’
================================================================================
1840 (System: 977) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/log/src/log_level/tag_log_level/linked_list/log_linked_list.c:108 NPD.GEN.MUST (1:Cri
tical) Analyze
Null pointer ’it’ that comes from line 106 will be dereferenced at line 108.
* log_linked_list.c:106: ’it’ has been assigned a NULL value.
* log_linked_list.c:108: ’it’ is dereferenced by passing argument 1 to function ’strcmp’.
Current status ’Analyze’
================================================================================
1841 (System: 978) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/log/src/log_level/tag_log_level/linked_list/log_linked_list.c:70 UNINIT.STACK.MUST (1
:Critical) Analyze
’it’ is used uninitialized in this function.
* log_linked_list.c:69: ’it’ is declared
* log_linked_list.c:70: ’it’ is used, but is uninitialized.
Current status ’Analyze’
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 255
================================================================================
1853 (System: 818) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c:380 CWARN.NOEFF
ECT.UCMP.GE (4:Review) Analyze
Comparison of unsigned value against 0 is always true
Current status ’Analyze’
================================================================================
1854 (System: 819) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c:394 CWARN.NOEFF
ECT.UCMP.GE (4:Review) Analyze
Comparison of unsigned value against 0 is always true
Current status ’Analyze’
================================================================================
1855 (System: 820) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c:374 CWARN.NOEFF
ECT.UCMP.LT (4:Review) Analyze
Comparison of unsigned value against 0 is always false
Current status ’Analyze’
================================================================================
1856 (System: 821) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/compo
nents/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c:388 CWARN.NOEFF
ECT.UCMP.LT (4:Review) Analyze
Comparison of unsigned value against 0 is always false
Current status ’Analyze’
================================================================================
1871 (System: 1751) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/porting/nimble/src/os_mbuf.c:232 UNINIT.STACK.MUST (1:Critical
) Analyze
’omp’ is used uninitialized in this function.
* os_mbuf.c:228: ’omp’ is declared
* os_mbuf.c:232: ’omp’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1872 (System: 1752) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/porting/nimble/src/os_mbuf.c:246 UNINIT.STACK.MUST (1:Critical
) Analyze
’omp’ is used uninitialized in this function.
* os_mbuf.c:242: ’omp’ is declared
* os_mbuf.c:246: ’omp’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1945 (System: 1125) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_l2cap_sig.c:226 CWARN.MEM.NONPOD (4:Review
) Analyze
Memory manipulation routine ’memset’ is applied to a non-POD object
Current status ’Analyze’
================================================================================
1947 (System: 1126) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
20_KW_Vantage_BLE_Nexus_ESP32_Report.txt Wed Jul 30 20:04:52 2025 267
onents/bt/host/nimble/nimble/nimble/host/src/ble_l2cap_sig.c:314 UNINIT.STACK.MUST (1:Criti
cal) Analyze
’proc’ is used uninitialized in this function.
* ble_l2cap_sig.c:308: ’proc’ is declared
* ble_l2cap_sig.c:314: ’proc’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
1950 (System: 1127) /var/lib/jenkins/workspace/Vantage/Vantage_BLE_Nexus_ESP32/esp-idf/comp
onents/bt/host/nimble/nimble/nimble/host/src/ble_l2cap_sig.c:1911 UNINIT.STACK.MUST (1:Crit
ical) Analyze
’next’ is used uninitialized in this function.
* ble_l2cap_sig.c:1895: ’next’ is declared
* ble_l2cap_sig.c:1911: ’next’ is used, but is uninitialized.
Current status ’Analyze’
================================================================================
