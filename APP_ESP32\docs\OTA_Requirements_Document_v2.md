# ESP32 BLE OTA System - Requirements Document v2.0

**Document Version:** 2.0  
**Date:** July 31, 2025  
**Project:** Vantage ESP32 BLE OTA System  
**Author:** Development Team  

## 1. Executive Summary

This document defines the functional and non-functional requirements for the ESP32 Bluetooth Low Energy (BLE) Over-The-Air (OTA) firmware update system. The system enables wireless firmware updates from Python clients to ESP32 devices using BLE communication with enhanced performance and reliability.

## 2. System Overview

### 2.1 Purpose
Enable secure, reliable, and efficient firmware updates for ESP32 devices over BLE without requiring physical access or USB connections, achieving transfer speeds of 40-50KB/s.

### 2.2 Scope
- BLE-based firmware transfer protocol with chunked data transfer
- ESP32 OTA manager using standard ESP-IDF OTA APIs
- Python client application with automatic device discovery
- Security bypass mode for OTA operations
- Error handling, recovery, and automatic reconnection
- Progress tracking and status reporting

### 2.3 Key Features
- **High Performance**: 40-50KB/s transfer rates (11.7x improvement over baseline)
- **Reliability**: Automatic reconnection and ACK retry mechanisms
- **Standard Compliance**: Uses ESP-IDF OTA APIs for maximum compatibility
- **Security**: Optional BLE bonding with configurable security levels
- **User-Friendly**: Automatic device discovery and batch file selection

## 3. Functional Requirements

### 3.1 Core OTA Functionality

#### FR-001: Firmware Transfer Protocol
- **Requirement:** System SHALL support chunked firmware transfer over BLE
- **Details:**
  - Chunk size: 500 bytes (512 - 8 byte header)
  - Header format: sequence_number(4) + size(2) + reserved(2)
  - Maximum firmware size: 16MB
  - Transfer rate: ≥40KB/s (target: 46KB/s)
  - ACK mechanism: Optional with retry logic

#### FR-002: BLE Service Implementation
- **Requirement:** ESP32 SHALL expose dedicated BLE GATT service for OTA
- **Service UUID:** `def09abc-5678-1234-def0-9abc56781234`
- **Characteristics:**
  - Data Transfer: `def09abc-5678-1234-def1-9abc56781234` (Write)
  - Control: `def09abc-5678-1234-def2-9abc56781234` (Write)
  - Status: `def09abc-5678-1234-def3-9abc56781234` (Read/Notify)
  - Info: `def09abc-5678-1234-def4-9abc56781234` (Write)

#### FR-003: OTA Commands
- **Requirement:** System SHALL support the following OTA commands:
  - `OTA_CMD_START` (0x01): Initialize OTA session
  - `OTA_CMD_STOP` (0x02): Stop current session
  - `OTA_CMD_END` (0x03): Complete OTA and restart
  - `OTA_CMD_ABORT` (0x04): Abort and cleanup

#### FR-004: Firmware Validation
- **Requirement:** System SHALL validate firmware integrity using ESP-IDF built-in validation
- **Validation Methods:**
  - ESP-IDF image header validation
  - Built-in integrity checks via esp_ota_end()
  - Automatic rollback protection
  - Size boundary validation

#### FR-005: Progress Tracking
- **Requirement:** System SHALL provide real-time progress tracking
- **Implementation:**
  - Milestone-based progress reporting (10%, 20%, etc.)
  - Byte-level progress tracking
  - Matching progress display on both client and ESP32
  - Transfer speed calculation and display

### 3.2 Connection Management

#### FR-006: Automatic Device Discovery
- **Requirement:** Python client SHALL automatically discover ESP32 devices
- **Implementation:**
  - BLE scanning with device name filtering
  - MAC address caching and selection
  - Multiple device support with user selection

#### FR-007: Connection Recovery
- **Requirement:** System SHALL handle BLE connection failures gracefully
- **Recovery Mechanisms:**
  - Automatic reconnection (up to 3 attempts)
  - Connection timeout handling (30 seconds)
  - Supervision timeout management (20 seconds)
  - Graceful error reporting

#### FR-008: Connection Parameters
- **Requirement:** System SHALL optimize BLE connection parameters
- **Parameters:**
  - Connection interval: 7.5-15ms
  - Supervision timeout: 20 seconds
  - MTU size: 517 bytes (preferred)
  - ACL buffer size: 517 bytes

### 3.3 Security Requirements

#### FR-009: Security Bypass Mode
- **Requirement:** System SHALL support security bypass for OTA operations
- **Implementation:**
  - Configurable security disable during OTA
  - Default: Security bypassed for OTA efficiency
  - Optional: Full security mode with bonding

#### FR-010: BLE Bonding Support
- **Requirement:** System SHALL support optional BLE bonding
- **Configuration:**
  - I/O Capability: NoInputNoOutput (Just Works)
  - Bonding: Configurable (default: disabled for OTA)
  - MITM Protection: Disabled
  - Secure Connections: Configurable

## 4. Non-Functional Requirements

### 4.1 Performance Requirements

#### NFR-001: Transfer Speed
- **Requirement:** Minimum transfer rate of 40KB/s
- **Target:** 46KB/s for optimal performance
- **Measurement:** 3.6MB firmware in <90 seconds
- **Achievement:** Current implementation achieves 11.7x speed improvement

#### NFR-002: Memory Usage
- **Requirement:** OTA system SHALL use <32KB RAM
- **Constraints:**
  - Chunk buffer: 1KB maximum
  - Status tracking: <1KB
  - BLE stack overhead: <30KB

#### NFR-003: Flash Usage
- **Requirement:** OTA code SHALL use <64KB flash
- **Allocation:**
  - OTA manager: <32KB
  - BLE service: <16KB
  - Utilities: <16KB

### 4.2 Reliability Requirements

#### NFR-004: Success Rate
- **Requirement:** >95% success rate under normal conditions
- **Conditions:**
  - Stable BLE connection
  - Valid firmware image
  - Sufficient power supply
  - Proper BLE configuration

#### NFR-005: Error Recovery
- **Requirement:** 100% recovery from connection failures
- **Recovery Time:** <10 seconds for reconnection
- **Implementation:** Automatic reconnection with exponential backoff

### 4.3 Compatibility Requirements

#### NFR-006: Platform Support
- **ESP32 Variants:** ESP32, ESP32-S3, ESP32-C3
- **ESP-IDF Version:** 5.4.1+
- **Python Version:** 3.8+
- **BLE Stack:** NimBLE

#### NFR-007: Client Compatibility
- **Operating Systems:** Windows, Linux, macOS
- **BLE Requirements:** Bluetooth 4.0+ with BLE support
- **Python Dependencies:** bleak, asyncio

## 5. Interface Requirements

### 5.1 BLE Interface

#### IR-001: MTU Requirements
- **Minimum MTU:** 23 bytes (BLE standard)
- **Preferred MTU:** 517 bytes
- **Negotiation:** Automatic MTU discovery
- **ACL Buffer:** 517 bytes for proper chunk handling

#### IR-002: Connection Parameters
- **Connection Interval:** 7.5-15ms (optimal)
- **Slave Latency:** 0
- **Supervision Timeout:** 20 seconds
- **PHY:** 1M (default), 2M (preferred)

### 5.2 Python Client Interface

#### IR-003: Command Line Interface
```bash
# Basic usage with automatic device discovery
python nimble_ota_client.py firmware.bin

# Advanced usage with specific device
python nimble_ota_client.py --address MAC_ADDR --verbose firmware.bin

# Batch file selection
python Ota_flash_firmware_select.bat
```

#### IR-004: Programmatic Interface
```python
client = NimBLEOTAClient(device_address, verbose=True)
await client.connect()
success = await client.transfer_firmware("firmware.bin")
```

## 6. Data Requirements

### 6.1 Firmware Information Structure
```c
typedef struct {
    uint32_t total_size;      // Total firmware size
    uint32_t chunk_size;      // Chunk size for transfer (500 bytes)
    uint32_t crc32;          // CRC32 checksum
    char version[16];        // Version string with timestamp
} ota_firmware_info_t;
```

### 6.2 Status Response Structure
```c
typedef struct {
    uint8_t status;          // OTA status code
    uint8_t error;           // Error code (if any)
    uint32_t sequence;       // Last processed sequence
    uint32_t bytes_received; // Total bytes received
} ota_status_response_t;
```

### 6.3 Chunk Header Structure
```c
typedef struct {
    uint32_t sequence_number;  // Chunk sequence (starts from 1)
    uint16_t chunk_size;       // Actual data size (500 bytes)
    uint16_t reserved;         // Reserved for future use
} ota_chunk_header_t;
```

## 7. Configuration Requirements

### 7.1 ESP32 Configuration
- **Partition Table:** Custom with factory, ota_0, ota_1 partitions
- **BLE Configuration:** NimBLE with optimized ACL buffers
- **OTA Configuration:** Standard ESP-IDF OTA with rollback protection

### 7.2 BLE Stack Configuration
```c
CONFIG_BT_NIMBLE_TRANSPORT_ACL_SIZE=517
CONFIG_BT_NIMBLE_ACL_BUF_SIZE=517
CONFIG_BT_NIMBLE_SVC_GAP_PPCP_SUPERVISION_TMO=2000
```

## 8. Testing Requirements

### 8.1 Functional Testing
- Normal OTA transfer completion
- Connection failure recovery
- ACK retry mechanism validation
- Progress tracking accuracy
- Multi-device support

### 8.2 Performance Testing
- Transfer speed benchmarking (target: 46KB/s)
- Memory usage profiling
- Connection stability testing
- Large firmware handling (3.6MB+)

### 8.3 Security Testing
- Security bypass functionality
- Optional bonding validation
- Connection parameter optimization

## 9. Success Criteria

### 9.1 Acceptance Criteria
- ✅ 95%+ success rate for valid firmware
- ✅ <90 second transfer time for 3.6MB firmware
- ✅ Automatic recovery from connection failures
- ✅ Zero firmware corruption incidents
- ✅ Cross-platform Python client compatibility
- ✅ 40+ KB/s transfer speed achievement

### 9.2 Performance Metrics
- Transfer speed: 46KB/s (achieved)
- Connection establishment: <5 seconds
- Error recovery time: <10 seconds
- Memory usage: <32KB RAM, <64KB Flash
- Success rate: >95%

---

**Document Control:**
- **Version:** 2.0
- **Last Updated:** July 31, 2025
- **Next Review:** August 31, 2025
- **Approved By:** Development Team
