/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : ble_app.h
* @version 		  : 1.0.1
* @brief          : Header for ble_app.c file
* @details		  : Header for ble_app.c file
********************************************************************************
* @version 1.0.1                                         				Date : 21/07/2025
* Added function declarations for BLE advertising control via UART
* Added ble_advertisement_start(), ble_advertisement_stop(), is_ble_synced()
* Added load_ble_adv_state_from_nvs() declaration for NVS state management
********************************************************************************/
#ifndef BLE_APP_H
#define BLE_APP_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "host/ble_hs.h"

#ifdef __cplusplus
extern "C"
{
#endif

int32_t ble_initialization(void);
bool get_stop_adv_on_disconnect_flag(void);
void set_stop_adv_on_disconnect_flag(bool state);
uint16_t get_connection_handle(void);
int update_ble_name_and_start_advertising(const char* new_name);
const char* get_current_ble_name(void);
void ble_advertisement_start(void);
void ble_advertisement_stop(void);
bool is_ble_synced(void);



#ifdef __cplusplus
}
#endif

#endif /* END OF BLE_APP_H */