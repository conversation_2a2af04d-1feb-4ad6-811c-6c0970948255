# BLE OTA Firmware Update System

This document describes the BLE-based Over-The-Air (OTA) firmware update system implemented for the ESP32 using NimBLE.

## Overview

The BLE OTA system allows firmware updates to be sent wirelessly from a Python client (mobile/PC) to the ESP32 device. The system includes:

- **Factory partition boot**: Device boots from factory partition for OTA updates
- **NimBLE BLE stack**: Custom GATT service for firmware transfer
- **Chunked transfer**: Firmware sent in manageable chunks with ACK/NACK
- **Resume capability**: Power failure recovery with state persistence
- **Image verification**: CRC32 verification before partition switching
- **Ping-pong OTA**: Automatic boot partition switching after successful update

## Architecture

### GATT Service Structure

**Service UUID**: `12345678-1234-1234-1234-123456789ABC`

| Characteristic | UUID | Properties | Description |
|----------------|------|------------|-------------|
| Data | `...AB1` | Write, Write No Response | Firmware chunk data |
| Control | `...AB2` | Read, Write | OTA commands (start/stop/reset) |
| Status | `...AB3` | Read, Notify | Status responses and ACK/NACK |
| Info | `...AB4` | Read, Write | Firmware information (size, CRC) |

### Data Flow

1. **Connection**: Python client connects to ESP32 BLE service
2. **Info Transfer**: Client sends firmware size, CRC, and version info
3. **Start Command**: Client sends START command to begin OTA
4. **Chunk Transfer**: Client sends firmware in chunks with sequence numbers
5. **Acknowledgment**: ESP32 sends ACK/NACK for each chunk
6. **Verification**: ESP32 verifies complete image CRC
7. **Completion**: ESP32 switches boot partition and restarts

## File Structure

### ESP32 Implementation

```
main/
├── Inc/
│   ├── ble_ota_service.h      # BLE OTA service definitions
│   └── ota_manager.h          # OTA manager interface
└── Src/
    ├── ble_ota_service.c      # BLE GATT service implementation
    ├── ota_manager.c          # OTA partition management
    └── main.c                 # Integration with main app
```

### Python Client

```
python_testing/
├── ble_ota_client.py         # BLE OTA client implementation
├── test_ble_ota.py           # Test suite and dummy firmware
└── requirements.txt          # Python dependencies
```

## Usage

### ESP32 Setup

1. **Build and Flash**: Build the firmware with OTA support
```bash
cd APP_ESP32
idf.py build
idf.py flash
```

2. **Monitor**: Check that BLE OTA service initializes
```bash
idf.py monitor
```

Expected output:
```
I (xxx) OTA_MANAGER: Initializing OTA manager
I (xxx) BLE_OTA_SERVICE: Initializing BLE OTA service
I (xxx) BLE_OTA_SERVICE: BLE OTA service initialized successfully
```

### Python Client Setup

1. **Install Dependencies**:
```bash
pip install bleak asyncio
```

2. **Prepare Firmware**: Have your firmware binary ready (e.g., `firmware.bin`)

3. **Run OTA Update**:
```bash
cd python_testing
python ble_ota_client.py firmware.bin --name "CT Car 8"
```

Or specify MAC address:
```bash
python ble_ota_client.py firmware.bin --address "A0:85:E3:F1:8C:C6"
```

### Testing

Run the test suite:
```bash
python test_ble_ota.py --live
```

## Protocol Details

### Firmware Info Structure
```c
typedef struct {
    uint32_t total_size;    // Total firmware size in bytes
    uint32_t chunk_size;    // Maximum chunk size (512 bytes)
    uint32_t crc32;         // Expected CRC32 of complete firmware
    uint8_t version[16];    // Version string (null-terminated)
} ota_firmware_info_t;
```

### Chunk Header Structure
```c
typedef struct {
    uint32_t sequence_number;  // Chunk sequence (starts from 1)
    uint16_t chunk_size;       // Actual data size in this chunk
    uint16_t reserved;         // Reserved for future use
} ota_chunk_header_t;
```

### Status Response Structure
```c
typedef struct {
    ota_status_t status;       // Current status (ACK/NACK/ERROR)
    ota_error_t error;         // Error code if status is ERROR/NACK
    uint32_t sequence_number;  // Last processed sequence number
    uint32_t bytes_received;   // Total bytes received so far
} ota_status_response_t;
```

## Resume Functionality

The system supports resume after power failure:

1. **State Persistence**: Progress saved to NVS every 10 chunks
2. **Magic Number**: Validates saved state integrity
3. **Resume Command**: Client can send RESUME command to continue
4. **Sequence Validation**: Ensures no chunks are missed

### Resume Process

1. ESP32 boots and loads saved state from NVS
2. Client connects and reads current status
3. If resume possible, client sends RESUME command
4. Client continues from last acknowledged sequence number

## Error Handling

### Error Codes

| Code | Name | Description |
|------|------|-------------|
| 0x00 | NONE | No error |
| 0x01 | INVALID_SIZE | Firmware too large for partition |
| 0x02 | WRITE_FAILED | Failed to write to OTA partition |
| 0x03 | VERIFY_FAILED | Image verification failed |
| 0x04 | PARTITION_FAILED | OTA partition operation failed |
| 0x05 | SEQUENCE_ERROR | Chunk sequence number error |
| 0x06 | CRC_MISMATCH | CRC verification failed |

### Retry Logic

- **Chunk Retries**: Up to 3 retries per chunk
- **Timeout**: 5-second timeout for ACK responses
- **Backoff**: 500ms delay between retries

## Security Considerations

### Current Implementation
- Basic CRC32 verification
- No encryption or authentication
- Open BLE connection

### Recommended Enhancements
- Add firmware signing and verification
- Implement BLE pairing/bonding
- Add encryption for firmware data
- Version rollback protection

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check ESP32 is advertising
   - Verify device name/address
   - Check BLE adapter on client

2. **Transfer Timeout**
   - Reduce chunk size
   - Check BLE signal strength
   - Increase timeout values

3. **Verification Failed**
   - Check firmware file integrity
   - Verify CRC calculation method
   - Check for transmission errors

4. **Partition Error**
   - Verify OTA partition table
   - Check available flash space
   - Ensure firmware size is valid

### Debug Logs

Enable debug logging:
```c
// In ESP32 code
#define LOG_LOCAL_LEVEL ESP_LOG_DEBUG
```

```python
# In Python client
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Performance

### Typical Transfer Rates
- **Chunk Size**: 512 bytes
- **Transfer Rate**: ~2-5 KB/s (depends on BLE connection)
- **100KB Firmware**: ~20-50 seconds

### Optimization Tips
- Use maximum MTU size
- Minimize ACK delays
- Optimize chunk size for your use case
- Use BLE connection parameters tuning

## Integration Notes

### Existing Functionality
- **No Impact**: OTA system doesn't affect existing UART, BLE GATT services
- **Coexistence**: Runs alongside existing BLE services
- **Factory Boot**: Only active when booting from factory partition

### Memory Usage
- **RAM**: ~8KB for buffers and state
- **Flash**: ~20KB for OTA code
- **NVS**: ~1KB for state persistence

## Future Enhancements

1. **Delta Updates**: Only transfer changed portions
2. **Compression**: Compress firmware before transfer
3. **Multi-file Support**: Support for multiple file types
4. **Progress UI**: Enhanced progress reporting
5. **Batch Updates**: Update multiple devices simultaneously
