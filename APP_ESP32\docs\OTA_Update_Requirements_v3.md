# BLE OTA Update System Requirements Document 

####  Platform Support
- **ESP32 Variants:**  ESP32-S3
- **ESP-IDF Version:** 5.4.1
- **Python Version:** 3.8+
- **BLE Stack:** NimBLE


####  Python Client Components
- **nimble_ota_client.py:** Main OTA client implementation
- **Ota_flash_firmware_select.bat:** Batch file interface
- **Device discovery and connection management**
- **Progress tracking and error handling**

## Installation and Dependencies

### ESP32 Development Environment Setup

#### ESP-IDF Installation
**Requirement:** ESP-IDF version 5.4.1 or higher

**Windows Installation:**
```bash
# Download and install ESP-IDF
# Visit: https://docs.espressif.com/projects/esp-idf/en/latest/esp32/get-started/windows-setup.html

# Alternative: Use ESP-IDF installer
# Download from: https://dl.espressif.com/dl/esp-idf/

# Verify installation
idf.py --version
```

**Linux/macOS Installation:**
```bash
# Install prerequisites
sudo apt-get install git wget flex bison gperf python3 python3-pip python3-venv cmake ninja-build ccache libffi-dev libssl-dev dfu-util libusb-1.0-0

# Clone ESP-IDF
mkdir -p ~/esp
cd ~/esp
git clone --recursive https://github.com/espressif/esp-idf.git
cd esp-idf
git checkout v5.4.1

# Install ESP-IDF
./install.sh esp32

# Setup environment
. ./export.sh
```

#### ESP32 Project Configuration
**Required Configuration Files:**

1. **Custom Partition Table** (`partitions.csv`):
```csv
# Name,   Type, SubType, Offset,  Size, Flags
nvs,      data, nvs,     0x9000,  16K,
otadata,  data, ota,     0xd000,  8K,
phy_init, data, phy,     0xf000,  4K,
factory,  app,  factory, 0x10000, 1M,
ota_0,    app,  ota_0,   0x110000,1M,
ota_1,    app,  ota_1,   0x210000,1M,
storage,  data, spiffs,  0x310000,20M,
```

2. **NimBLE Configuration** (`sdkconfig` additions):
```ini
# BLE Configuration
CONFIG_BT_ENABLED=y
CONFIG_BT_NIMBLE_ENABLED=y
CONFIG_BT_NIMBLE_TRANSPORT_ACL_SIZE=517
CONFIG_BT_NIMBLE_ACL_BUF_SIZE=517
CONFIG_BT_NIMBLE_SVC_GAP_PPCP_SUPERVISION_TMO=2000

# Partition Configuration
CONFIG_PARTITION_TABLE_CUSTOM=y
CONFIG_PARTITION_TABLE_CUSTOM_FILENAME="partitions.csv"

# OTA Configuration
CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE=y
CONFIG_BOOTLOADER_ROLLBACK_ENABLE=y

# Logging Configuration
CONFIG_LOG_DEFAULT_LEVEL_INFO=y
CONFIG_BOOTLOADER_LOG_LEVEL_INFO=y
```

#### ESP32 Build and Flash
```bash
# Navigate to project directory
cd APP_ESP32

# Configure project
idf.py menuconfig

# Build project
idf.py build

# Flash firmware
idf.py -p COM3 flash monitor  # Windows
idf.py -p /dev/ttyUSB0 flash monitor  # Linux
```

### Python Client Environment Setup

#### Python Installation
**Requirement:** Python 3.8 or higher

**Windows:**
```bash
# Download from python.org or use Microsoft Store
# Verify installation
python --version
pip --version
```

**Linux:**
```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install python3 python3-pip python3-venv

# CentOS/RHEL
sudo yum install python3 python3-pip

# Verify installation
python3 --version
pip3 --version
```

**macOS:**
```bash
# Using Homebrew
brew install python3

# Verify installation
python3 --version
pip3 --version
```

#### Python Dependencies Installation

**Core Dependencies:**
```bash
# Create virtual environment (recommended)
python -m venv ota_env

# Activate virtual environment
# Windows:
ota_env\Scripts\activate
# Linux/macOS:
source ota_env/bin/activate

# Install required packages
pip install bleak>=0.20.0
pip install asyncio-mqtt>=0.11.0  # Optional for MQTT support
pip install pyserial>=3.5         # For serial communication
pip install crcmod>=1.7           # For CRC calculations
```

**Complete requirements.txt:**
```txt
bleak>=0.20.0
asyncio>=3.4.3
pyserial>=3.5
crcmod>=1.7
colorama>=0.4.4
click>=8.0.0
```

**Install from requirements file:**
```bash
pip install -r requirements.txt
```

#### BLE Adapter Requirements

**Windows:**
- Windows 10 version 1903 or later
- Built-in Bluetooth adapter or USB Bluetooth 4.0+ dongle
- Windows Bluetooth stack (not third-party drivers)

**Linux:**
- BlueZ 5.43 or later
- Bluetooth adapter with BLE support
```bash
# Install BlueZ
sudo apt-get install bluez bluez-tools

# Check BlueZ version
bluetoothctl --version

# Enable Bluetooth service
sudo systemctl enable bluetooth
sudo systemctl start bluetooth
```

**macOS:**
- macOS 10.15 (Catalina) or later
- Built-in Bluetooth or compatible USB adapter
- Core Bluetooth framework support

### Development Tools (Optional)

#### Code Editors and IDEs
- **VS Code** with ESP-IDF extension
- **Eclipse** with ESP-IDF plugin
- **CLion** with ESP-IDF plugin

#### Debugging Tools
```bash
# ESP32 debugging
pip install esptool
pip install esp-idf-monitor

# BLE debugging tools
# Windows: Use built-in Bluetooth settings
# Linux: bluetoothctl, hcitool, gatttool
sudo apt-get install bluetooth bluez-utils

# Python debugging
pip install pdb-attach
pip install ipython
```

### Verification and Testing

#### ESP32 Verification
```bash
# Check ESP32 connection
idf.py -p COM3 monitor

# Verify BLE stack
# Look for "BLE Host Task Started" in logs

# Test OTA service
# Check for "BLE OTA service initialized successfully"
```

#### Python Client Verification
```python
# Test BLE functionality
import asyncio
from bleak import BleakScanner

async def test_ble():
    devices = await BleakScanner.discover()
    print(f"Found {len(devices)} BLE devices")
    for device in devices:
        print(f"  {device.name}: {device.address}")

# Run test
asyncio.run(test_ble())
```

#### End-to-End Test
```bash
# Run OTA client
cd APP_ESP32
python nimble_ota_client.py

# Or use batch file
Ota_flash_firmware_select.bat
```

### Troubleshooting Common Installation Issues

#### ESP-IDF Issues
- **Permission denied on serial port:** Add user to dialout group (Linux)
  ```bash
  sudo usermod -a -G dialout $USER
  # Logout and login again
  ```
- **Python not found:** Ensure Python is in PATH
- **Git submodule errors:** Run `git submodule update --init --recursive`
- **Build errors:** Check ESP-IDF version compatibility

#### Python BLE Issues
- **Bleak import error:** Ensure correct Python version and virtual environment
- **BLE adapter not found:** Check Bluetooth service status
  ```bash
  # Windows: Check Device Manager
  # Linux: sudo systemctl status bluetooth
  # macOS: System Preferences > Bluetooth
  ```
- **Permission denied:** Run with appropriate privileges or add user to bluetooth group
  ```bash
  sudo usermod -a -G bluetooth $USER
  ```

#### Connection Issues
- **Device not found:**
  - Verify ESP32 is advertising and in range
  - Check device name starts with "MR", "CT", "COP", or contains "ESP32"
- **Connection timeout:**
  - Check BLE adapter compatibility
  - Verify supervision timeout settings
- **Transfer failures:**
  - Verify MTU size and connection parameters
  - Check for notification enable failures

#### Notification Enable Failures
**Current Known Issue:** `[ERROR] Failed to send start command: Not connected`

**Root Cause:** BLE notification enable failure causes connection loss

**Workaround:**
1. Check ESP32 BLE connection handle management
2. Verify notification characteristic is properly configured
3. Use polling mode fallback when notifications fail

**Debug Steps:**
```bash
# Enable verbose logging
python nimble_ota_client.py --verbose

# Check ESP32 logs
idf.py -p COM3 monitor

# Look for:
# - "BLE CONNECTION ESTABLISHED"
# - "subscribe event" logs
# - "OTA service initialized successfully"
```

### Quick Start Guide

#### For ESP32 Development:
1. Install ESP-IDF 5.4.1+
2. Configure custom partition table
3. Set NimBLE configuration
4. Build and flash firmware
5. Verify BLE OTA service initialization

#### For Python Client:
1. Install Python 3.8+
2. Create virtual environment
3. Install dependencies: `pip install bleak>=0.20.0`
4. Test BLE functionality
5. Run OTA client: `Ota_flash_firmware_select.bat`

#### Minimum Working Setup:
```bash
# ESP32 side
idf.py build flash monitor

# Python side
pip install bleak
python nimble_ota_client.py
```
