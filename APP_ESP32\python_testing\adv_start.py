import serial
import time

# Replace with your serial port and baud rate
# SERIAL_PORT = "/dev/ttyUSB0"   # Example for Linux. On Windows, it might be "COM3"
SERIAL_PORT = "COM16"
BAUD_RATE = 1500000
OUTPUT_FILE = "output.bin"     # Name of the output binary file
hex_value = 'A501000000004E0873C5'
end_data = 'A5330200000001019229D03D'
RESPONSE = bytes.fromhex(hex_value)
END_DATA = bytes.fromhex(end_data)


def main():
    data = [0xA5, 0x10, 0x08, 0x00, 0x00, 0x00, 0x43, 0x54, 0x20, 
        0x43, 0x61, 0x72, 0x20, 0x35, 0x68, 0xB3, 0xAB, 0xD6]
    length = 0
    try:
        with serial.Serial(SERIAL_PORT, BAUD_RATE, timeout=1) as ser:
            print(f"Listening on {SERIAL_PORT} at {BAUD_RATE} baud rate.")
            ser.write(data)
            time.sleep(3)
            ser.write(END_DATA)
            # while True:
            #     data = ser.read(14)  # Reads up to 512 bytes or until timeout
            #     if data:
            #         print("Len of data: ", len(data))
            #         print(" ".join(f"0x{b:02X}" for b in data))
            #         if len(data) == 14 or len(data) == ((256*1024) + 10):
            #             ser.write(RESPONSE)
                        
    except serial.SerialException as e:
        print(f"Serial exception: {e}")

if __name__ == "__main__":
    main()


